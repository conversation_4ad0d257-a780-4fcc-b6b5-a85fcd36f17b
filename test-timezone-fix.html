<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Calendar Timezone Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .mismatch {
            background-color: #ffebee;
        }
        .match {
            background-color: #e8f5e8;
        }
    </style>
</head>
<body>
    <h1>🔧 Google Calendar Timezone Fix Test</h1>
    <p>This test verifies that the timezone handling fix for Google Calendar events is working correctly.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="runTimezoneTests()">🧪 Run Timezone Tests</button>
        <button onclick="testBackgroundTransformation()">🔄 Test Background Transformation</button>
        <button onclick="testFrontendTransformation()">🎨 Test Frontend Transformation</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Mock Google Calendar API event data for testing
        const mockGoogleEvents = [
            {
                id: 'test-all-day-1',
                summary: 'All Day Event Test',
                start: { date: '2024-01-15' },
                end: { date: '2024-01-16' },
                description: 'Test all-day event'
            },
            {
                id: 'test-timed-1',
                summary: 'Timed Event Test',
                start: { dateTime: '2024-01-15T14:30:00-08:00' },
                end: { dateTime: '2024-01-15T15:30:00-08:00' },
                description: 'Test timed event'
            },
            {
                id: 'test-utc-timed',
                summary: 'UTC Timed Event',
                start: { dateTime: '2024-01-15T22:30:00Z' },
                end: { dateTime: '2024-01-15T23:30:00Z' },
                description: 'Test UTC timed event'
            }
        ];

        // Background.js transformation (FIXED version)
        function backgroundTransformation(event) {
            const isAllDay = !event.start.dateTime;
            let startDate, endDate;

            if (isAllDay) {
                // FIXED: Added 'Z' suffix for UTC interpretation
                startDate = new Date(event.start.date + 'T00:00:00Z');
                endDate = event.end.date ? new Date(event.end.date + 'T00:00:00Z') : startDate;
            } else {
                startDate = new Date(event.start.dateTime);
                endDate = event.end.dateTime ? new Date(event.end.dateTime) : startDate;
            }

            return {
                id: `google_${event.id}`,
                title: event.summary || 'Untitled Event',
                description: event.description || '',
                date: startDate.toISOString(),
                endTime: isAllDay ? null : endDate.toISOString(),
                isFullDay: isAllDay,
                isGoogleEvent: true,
                googleEventId: event.id
            };
        }

        // Frontend transformation (from google-calendar-service.js)
        function frontendTransformation(googleEvent) {
            const isAllDay = !googleEvent.start.dateTime;
            let startDate, endDate;

            if (isAllDay) {
                startDate = new Date(googleEvent.start.date + 'T00:00:00Z');
                endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00Z') : startDate;
            } else {
                startDate = new Date(googleEvent.start.dateTime);
                endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
            }

            return {
                id: `google_${googleEvent.id}`,
                title: googleEvent.summary || 'Untitled Event',
                description: googleEvent.description || '',
                date: startDate,
                endTime: isAllDay ? null : endDate,
                isFullDay: isAllDay,
                isGoogleEvent: true,
                googleEventId: googleEvent.id
            };
        }

        // Calendar.js formatDate function (uses UTC)
        function formatDate(date) {
            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function runTimezoneTests() {
            addResult('🧪 Running Timezone Tests', 'Testing timezone handling consistency...', 'info');
            
            let allTestsPassed = true;
            const results = [];

            mockGoogleEvents.forEach((event, index) => {
                const backgroundResult = backgroundTransformation(event);
                const frontendResult = frontendTransformation(event);
                
                // Convert frontend result to match background format (ISO strings)
                const frontendForComparison = {
                    ...frontendResult,
                    date: frontendResult.date.toISOString(),
                    endTime: frontendResult.endTime ? frontendResult.endTime.toISOString() : null
                };

                const dateMatch = backgroundResult.date === frontendForComparison.date;
                const endTimeMatch = backgroundResult.endTime === frontendForComparison.endTime;
                const testPassed = dateMatch && endTimeMatch;
                
                if (!testPassed) allTestsPassed = false;

                results.push({
                    event: event.summary,
                    type: event.start.dateTime ? 'Timed' : 'All-day',
                    backgroundDate: backgroundResult.date,
                    frontendDate: frontendForComparison.date,
                    dateMatch,
                    backgroundEndTime: backgroundResult.endTime,
                    frontendEndTime: frontendForComparison.endTime,
                    endTimeMatch,
                    testPassed
                });
            });

            // Create comparison table
            let tableHtml = `
                <table class="comparison-table">
                    <tr>
                        <th>Event</th>
                        <th>Type</th>
                        <th>Background Date</th>
                        <th>Frontend Date</th>
                        <th>Match</th>
                        <th>End Time Match</th>
                        <th>Result</th>
                    </tr>
            `;

            results.forEach(result => {
                const rowClass = result.testPassed ? 'match' : 'mismatch';
                tableHtml += `
                    <tr class="${rowClass}">
                        <td>${result.event}</td>
                        <td>${result.type}</td>
                        <td>${result.backgroundDate}</td>
                        <td>${result.frontendDate}</td>
                        <td>${result.dateMatch ? '✅' : '❌'}</td>
                        <td>${result.endTimeMatch ? '✅' : '❌'}</td>
                        <td>${result.testPassed ? '✅ PASS' : '❌ FAIL'}</td>
                    </tr>
                `;
            });

            tableHtml += '</table>';

            const summary = `
TIMEZONE TEST RESULTS:
${allTestsPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}

Tests run: ${results.length}
Passed: ${results.filter(r => r.testPassed).length}
Failed: ${results.filter(r => !r.testPassed).length}

${tableHtml}
            `;

            addResult('📊 Timezone Test Results', summary, allTestsPassed ? 'success' : 'error');
        }

        function testBackgroundTransformation() {
            addResult('🔄 Testing Background Transformation', 'Running background.js transformation...', 'info');
            
            const results = mockGoogleEvents.map(event => {
                const transformed = backgroundTransformation(event);
                const parsedDate = new Date(transformed.date);
                const formattedDate = formatDate(parsedDate);
                
                return {
                    original: event,
                    transformed,
                    parsedDate: parsedDate.toISOString(),
                    formattedDate,
                    isValidDate: !isNaN(parsedDate.getTime())
                };
            });

            const output = JSON.stringify(results, null, 2);
            addResult('📋 Background Transformation Results', output, 'info');
        }

        function testFrontendTransformation() {
            addResult('🎨 Testing Frontend Transformation', 'Running google-calendar-service.js transformation...', 'info');
            
            const results = mockGoogleEvents.map(event => {
                const transformed = frontendTransformation(event);
                const formattedDate = formatDate(transformed.date);
                
                return {
                    original: event,
                    transformed: {
                        ...transformed,
                        date: transformed.date.toISOString(),
                        endTime: transformed.endTime ? transformed.endTime.toISOString() : null
                    },
                    formattedDate,
                    isValidDate: !isNaN(transformed.date.getTime())
                };
            });

            const output = JSON.stringify(results, null, 2);
            addResult('📋 Frontend Transformation Results', output, 'info');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to test timezone fixes. Click "Run Timezone Tests" to verify the fix.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
