<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CalenTask</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- No external libraries needed -->
</head>
<body>
    <div class="dashboard">
        <!-- Left Panel: Task Management -->
        <div class="task-panel">
            <header>
                <div class="header-container">
                    <h1 class="todo-header">CalenTask</h1>
                    <button id="settings-btn" class="icon-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </header>

            <div class="task-input">
                <input type="text" id="task-title" placeholder="Add a new task...">
                <textarea id="task-description" placeholder="Description (optional)"></textarea>
                <div class="form-actions">
                    <button id="add-task">Add Task</button>
                    <span class="shortcut-hint"><i class="fas fa-keyboard"></i> Press Ctrl + Enter to add</span>
                </div>
            </div>

            <div class="task-list-container">
                <h2>Your Tasks</h2>
                <ul id="task-list" class="task-list"></ul>
            </div>

            <div class="archive-container">
                <div class="archive-header">
                    <h2>Completed Tasks</h2>
                    <button id="toggle-archive" class="toggle-btn" title="Toggle archive visibility"><i class="fas fa-chevron-down"></i></button>
                </div>
                <ul id="archive-list" class="task-list archive-list"></ul>
            </div>
        </div>

        <!-- Right Panel: Calendar -->
        <div class="calendar-panel">
            <div class="calendar-header">
                <h2>Weekly Calendar</h2>
            </div>
            <div id="calendar"></div>
        </div>
    </div>

    <div id="edit-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Edit Task</h2>
            <input type="text" id="edit-title" placeholder="Task title">
            <textarea id="edit-description" placeholder="Description (optional)"></textarea>
            <div class="form-actions">
                <button id="save-edit">Save Changes</button>
                <span class="shortcut-hint"><i class="fas fa-keyboard"></i> Press Ctrl + Enter to save</span>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <h2>Settings</h2>

            <div class="settings-tabs">
                <button class="tab-btn active" data-tab="blocklist">URL Blocklist</button>
                <button class="tab-btn" data-tab="notifications">Notifications</button>
                <button class="tab-btn" data-tab="google-calendar">Google Calendar</button>
            </div>

            <div class="tab-content active" id="blocklist-tab">
                <div class="blocking-toggle">
                    <label class="toggle-switch">
                        <input type="checkbox" id="blocking-enabled" checked>
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="toggle-label">Block websites during scheduled task times</span>
                </div>

                <p>Add URLs you want to block when using CalenTask:</p>

                <div class="blocklist-input">
                    <input type="text" id="blocklist-url" placeholder="Enter URL to block (e.g., facebook.com)">
                    <button id="add-url-btn">Add URL</button>
                </div>

                <div class="validation-message" id="url-validation"></div>

                <div class="blocklist-container">
                    <h3>Blocked URLs</h3>
                    <ul id="blocklist" class="blocklist"></ul>
                </div>

                <div class="blocking-info">
                    <p><i class="fas fa-info-circle"></i> Websites will only be blocked during your scheduled task times.</p>
                    <p class="stats-info">Blocked attempts: <span id="blocked-attempts">0</span></p>
                </div>
            </div>

            <!-- Notification Settings Tab -->
            <div class="tab-content" id="notifications-tab">
                <div class="notification-settings">
                    <h3>Task Notifications</h3>
                    <p>Enable browser notifications for time-specific tasks:</p>

                    <div class="notification-toggle">
                        <label class="toggle-switch">
                            <input type="checkbox" id="notifications-enabled" checked>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Show notifications when tasks start and end</span>
                    </div>

                    <div class="notification-permission-status">
                        <p>Notification permission: <span id="notification-permission-status">Checking...</span></p>
                        <button id="request-notification-permission" class="hidden">Request Permission</button>
                    </div>

                    <div class="notification-info">
                        <p><i class="fas fa-info-circle"></i> Notifications will only be shown for time-specific tasks (not full-day events).</p>
                        <p>You'll receive a notification when a scheduled task starts and when it ends.</p>
                    </div>
                </div>
            </div>

            <!-- Google Calendar Settings Tab -->
            <div class="tab-content" id="google-calendar-tab">
                <div class="google-calendar-settings">
                    <h3>Google Calendar Integration</h3>
                    <p>Connect your Google Calendar to view your events alongside CalenTask events:</p>

                    <div class="google-auth-section">
                        <div class="auth-status" id="google-auth-status">
                            <span class="status-text">Not connected</span>
                            <span class="user-info" id="google-user-info" style="display: none;"></span>
                        </div>

                        <div class="auth-buttons">
                            <button id="google-sign-in-btn" class="primary-btn">Sign in with Google</button>
                            <button id="google-sign-out-btn" class="secondary-btn" style="display: none;">Sign out</button>
                        </div>
                    </div>

                    <div class="google-calendar-toggle" id="google-calendar-toggle-section" style="display: none;">
                        <label class="toggle-switch">
                            <input type="checkbox" id="google-calendar-enabled">
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label">Show Google Calendar events in weekly view</span>
                    </div>

                    <div class="google-calendar-sync" id="google-calendar-sync-section" style="display: none;">
                        <div class="sync-status">
                            <p>Last sync: <span id="google-calendar-last-sync">Never</span></p>
                            <div class="sync-buttons">
                                <button id="google-calendar-sync-btn" class="secondary-btn">Sync now</button>
                                <button id="sync-status-dashboard-btn" class="secondary-btn">📊 Sync Status</button>
                            </div>
                        </div>

                        <div class="sync-info">
                            <p>Events synced: <span id="google-calendar-event-count">0</span></p>
                        </div>
                    </div>

                    <div class="google-calendar-info">
                        <p><i class="fas fa-info-circle"></i> Google Calendar events are read-only and cannot be edited in CalenTask.</p>
                        <p>Google Calendar events are displayed with a blue color and calendar icon (📅) to distinguish them from CalenTask events.</p>
                        <p>Your calendar data is only stored locally and is never sent to external servers.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application JS -->
    <script src="local-storage-manager.js"></script>
    <script src="sync-strategy-manager.js"></script>
    <script src="sync-status-dashboard.js"></script>
    <script src="google-auth.js"></script>
    <script src="google-calendar-service.js"></script>
    <script src="calendar.js"></script>
    <script src="notification-utils.js"></script>
    <script src="todo.js"></script>
</body>
</html>
