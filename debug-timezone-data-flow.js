// Comprehensive debug script to trace Google Calendar timezone data flow
// Run this in the browser console on the CalenTask extension page

console.log('🔍 Starting comprehensive timezone data flow debug...');

// Helper function to format time for comparison
function formatTimeForComparison(date, label) {
    if (!date) return `${label}: null`;
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return `${label}: Invalid Date`;
    
    return `${label}: ${d.toISOString()} (UTC: ${d.getUTCHours()}:${d.getUTCMinutes().toString().padStart(2, '0')}, Local: ${d.getHours()}:${d.getMinutes().toString().padStart(2, '0')})`;
}

// Function to analyze a specific event through the entire pipeline
function analyzeEventDataFlow(originalGoogleEvent) {
    console.log('\n🔬 ANALYZING EVENT DATA FLOW');
    console.log('================================');
    console.log('Original Google Calendar API event:', originalGoogleEvent);
    
    // Step 1: Background.js transformation
    console.log('\n📦 Step 1: Background.js transformation');
    const isAllDay = !originalGoogleEvent.start.dateTime;
    let backgroundStartDate, backgroundEndDate;
    
    if (isAllDay) {
        backgroundStartDate = new Date(originalGoogleEvent.start.date + 'T00:00:00Z');
        backgroundEndDate = originalGoogleEvent.end.date ? new Date(originalGoogleEvent.end.date + 'T00:00:00Z') : backgroundStartDate;
    } else {
        backgroundStartDate = new Date(originalGoogleEvent.start.dateTime);
        backgroundEndDate = originalGoogleEvent.end.dateTime ? new Date(originalGoogleEvent.end.dateTime) : backgroundStartDate;
    }
    
    const backgroundEvent = {
        id: `google_${originalGoogleEvent.id}`,
        title: originalGoogleEvent.summary || 'Untitled Event',
        date: backgroundStartDate.toISOString(),
        endTime: isAllDay ? null : backgroundEndDate.toISOString(),
        isFullDay: isAllDay,
        isGoogleEvent: true
    };
    
    console.log('Background transformed event:', backgroundEvent);
    console.log(formatTimeForComparison(originalGoogleEvent.start.dateTime || originalGoogleEvent.start.date, 'Original API time'));
    console.log(formatTimeForComparison(backgroundEvent.date, 'Background result'));
    
    // Step 2: Frontend deserialization (from storage)
    console.log('\n🎨 Step 2: Frontend deserialization');
    const frontendEvent = {
        ...backgroundEvent,
        date: new Date(backgroundEvent.date),
        endTime: backgroundEvent.endTime ? new Date(backgroundEvent.endTime) : null
    };
    
    console.log('Frontend deserialized event:', frontendEvent);
    console.log(formatTimeForComparison(frontendEvent.date, 'Frontend date object'));
    
    // Step 3: Calendar rendering logic
    console.log('\n📅 Step 3: Calendar rendering logic');
    const eventDate = new Date(frontendEvent.date);
    const dateStr = formatDateUTC(eventDate);
    const hour = eventDate.getUTCHours();
    const minutes = eventDate.getUTCMinutes();
    
    const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
    const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);
    
    console.log(`Calendar processing:
    - Event date: ${eventDate.toISOString()}
    - Date string: ${dateStr}
    - UTC Hour: ${hour}, UTC Minutes: ${minutes}
    - Rounded minutes: ${roundedMinutes}, Adjusted hour: ${adjustedHour}
    - Looking for cell at: ${dateStr} ${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')}`);
    
    // Step 4: Check what calendar cells exist
    console.log('\n🔍 Step 4: Calendar cell analysis');
    const cells = document.querySelectorAll('.time-cell');
    console.log(`Total calendar cells found: ${cells.length}`);
    
    // Find cells for the same date
    const matchingDateCells = [];
    cells.forEach((cell, index) => {
        const cellTimeAttr = cell.getAttribute('data-time');
        if (cellTimeAttr) {
            const cellTime = new Date(cellTimeAttr);
            const cellDateStr = formatDateUTC(cellTime);
            
            if (cellDateStr === dateStr) {
                matchingDateCells.push({
                    index,
                    cellTime,
                    cellTimeAttr,
                    utcHour: cellTime.getUTCHours(),
                    utcMinutes: cellTime.getUTCMinutes(),
                    localHour: cellTime.getHours(),
                    localMinutes: cellTime.getMinutes()
                });
            }
        }
    });
    
    console.log(`Cells for date ${dateStr}:`, matchingDateCells.slice(0, 10)); // Show first 10
    
    // Step 5: Find the exact matching cell
    const matchingCell = matchingDateCells.find(cell => 
        cell.utcHour === adjustedHour && cell.utcMinutes === roundedMinutes
    );
    
    if (matchingCell) {
        console.log('✅ Found matching cell:', matchingCell);
    } else {
        console.log('❌ No matching cell found!');
        console.log('Available time slots for this date:');
        matchingDateCells.forEach(cell => {
            console.log(`  ${cell.utcHour}:${cell.utcMinutes.toString().padStart(2, '0')} UTC (${cell.localHour}:${cell.localMinutes.toString().padStart(2, '0')} local)`);
        });
    }
    
    return {
        original: originalGoogleEvent,
        background: backgroundEvent,
        frontend: frontendEvent,
        calendar: {
            dateStr,
            hour,
            minutes,
            adjustedHour,
            roundedMinutes
        },
        matchingCell
    };
}

// UTC date formatter (matching calendar.js)
function formatDateUTC(date) {
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Function to check current timezone and create test events
function runComprehensiveTimezoneDebug() {
    console.log('\n🌍 TIMEZONE ENVIRONMENT INFO');
    console.log('=============================');
    
    const now = new Date();
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const timezoneOffset = now.getTimezoneOffset();
    
    console.log(`User timezone: ${userTimezone}`);
    console.log(`Timezone offset: ${timezoneOffset} minutes (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})`);
    console.log(`Current time: ${now.toISOString()} UTC`);
    console.log(`Current local time: ${now.toString()}`);
    
    // Create test events with specific times
    const testEvents = [
        {
            id: 'test-2pm-local',
            summary: '2:00 PM Local Time Test',
            start: { dateTime: '2024-01-15T14:00:00-08:00' }, // 2 PM PST
            end: { dateTime: '2024-01-15T15:00:00-08:00' }
        },
        {
            id: 'test-2pm-utc',
            summary: '2:00 PM UTC Test',
            start: { dateTime: '2024-01-15T14:00:00Z' }, // 2 PM UTC
            end: { dateTime: '2024-01-15T15:00:00Z' }
        },
        {
            id: 'test-all-day',
            summary: 'All Day Test',
            start: { date: '2024-01-15' },
            end: { date: '2024-01-16' }
        }
    ];
    
    console.log('\n🧪 TESTING SPECIFIC EVENTS');
    console.log('===========================');
    
    testEvents.forEach((event, index) => {
        console.log(`\n--- Test Event ${index + 1}: ${event.summary} ---`);
        analyzeEventDataFlow(event);
    });
    
    // Check actual Google Calendar events if available
    console.log('\n📊 CHECKING ACTUAL GOOGLE CALENDAR EVENTS');
    console.log('==========================================');
    
    // Try to get actual events from storage
    if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['google_calendar_events'], (result) => {
            const actualEvents = result.google_calendar_events || [];
            console.log(`Found ${actualEvents.length} actual Google Calendar events in storage`);
            
            if (actualEvents.length > 0) {
                console.log('\nAnalyzing first actual event:');
                const firstEvent = actualEvents[0];
                
                // Reconstruct what the original API event might have looked like
                const reconstructedOriginal = {
                    id: firstEvent.googleEventId,
                    summary: firstEvent.title,
                    start: firstEvent.isFullDay ? 
                        { date: new Date(firstEvent.date).toISOString().split('T')[0] } :
                        { dateTime: firstEvent.date },
                    end: firstEvent.endTime ? 
                        (firstEvent.isFullDay ? 
                            { date: new Date(firstEvent.endTime).toISOString().split('T')[0] } :
                            { dateTime: firstEvent.endTime }) :
                        null
                };
                
                analyzeEventDataFlow(reconstructedOriginal);
            }
        });
    }
}

// Function to test the specific timezone fix
function testTimezoneFixSpecifically() {
    console.log('\n🔧 TESTING SPECIFIC TIMEZONE FIX');
    console.log('================================');

    // Test case: Event that should appear at 2:00 PM
    const testEvent = {
        id: 'timezone-test',
        summary: '2:00 PM Test Event',
        start: { dateTime: '2024-01-15T14:00:00-08:00' }, // 2 PM PST = 10 PM UTC
        end: { dateTime: '2024-01-15T15:00:00-08:00' }
    };

    console.log('🧪 Test Event: 2:00 PM PST (should appear at 2:00 PM local time)');
    console.log('Original API dateTime:', testEvent.start.dateTime);

    // Step 1: Parse the original time
    const originalDate = new Date(testEvent.start.dateTime);
    console.log('Parsed original date:', originalDate.toISOString());
    console.log('Original UTC time:', `${originalDate.getUTCHours()}:${originalDate.getUTCMinutes().toString().padStart(2, '0')}`);
    console.log('Original local time:', `${originalDate.getHours()}:${originalDate.getMinutes().toString().padStart(2, '0')}`);

    // Step 2: Background transformation
    const backgroundEvent = {
        date: originalDate.toISOString(),
        isFullDay: false,
        isGoogleEvent: true
    };

    console.log('\n📦 Background transformation result:');
    console.log('Stored date:', backgroundEvent.date);

    // Step 3: Frontend deserialization
    const frontendDate = new Date(backgroundEvent.date);
    console.log('\n🎨 Frontend deserialization:');
    console.log('Frontend date object:', frontendDate.toISOString());
    console.log('Frontend UTC time:', `${frontendDate.getUTCHours()}:${frontendDate.getUTCMinutes().toString().padStart(2, '0')}`);
    console.log('Frontend local time:', `${frontendDate.getHours()}:${frontendDate.getMinutes().toString().padStart(2, '0')}`);

    // Step 4: Calendar cell matching
    const dateStr = formatDateUTC(frontendDate);
    const hour = frontendDate.getUTCHours();
    const minutes = frontendDate.getUTCMinutes();
    const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
    const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);

    console.log('\n📅 Calendar cell matching:');
    console.log(`Event date string: ${dateStr}`);
    console.log(`Event UTC time: ${hour}:${minutes.toString().padStart(2, '0')}`);
    console.log(`Looking for cell: ${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')} UTC`);

    // Step 5: Expected result
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const timezoneOffset = new Date().getTimezoneOffset();

    console.log('\n🎯 EXPECTED RESULT:');
    console.log(`User timezone: ${userTimezone} (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})`);
    console.log(`Original event time: 2:00 PM PST`);
    console.log(`Should appear in calendar at: ${frontendDate.getHours()}:${frontendDate.getMinutes().toString().padStart(2, '0')} local time`);
    console.log(`Calendar should find cell at: ${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')} UTC`);

    return {
        originalTime: testEvent.start.dateTime,
        utcTime: frontendDate.toISOString(),
        localDisplayTime: `${frontendDate.getHours()}:${frontendDate.getMinutes().toString().padStart(2, '0')}`,
        cellLookup: `${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')} UTC`,
        dateStr: dateStr
    };
}

// Auto-run the comprehensive debug
runComprehensiveTimezoneDebug();

// Run the specific timezone fix test
testTimezoneFixSpecifically();

// Export functions for manual testing
window.timezoneDebug = {
    analyzeEvent: analyzeEventDataFlow,
    runFull: runComprehensiveTimezoneDebug,
    testFix: testTimezoneFixSpecifically,
    formatTime: formatTimeForComparison
};

console.log('\n💡 Use window.timezoneDebug.analyzeEvent(event) to analyze specific events');
console.log('💡 Use window.timezoneDebug.runFull() to re-run full analysis');
console.log('💡 Use window.timezoneDebug.testFix() to test the specific timezone fix');
