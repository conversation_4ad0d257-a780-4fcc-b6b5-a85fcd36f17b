<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Timezone Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #e8f5e8;
            border: 1px solid #4CAF50;
        }
        .fail {
            background-color: #ffebee;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>🔧 Timezone Fix Verification</h1>
    <p>This test verifies that the complete timezone fix resolves the 7-hour offset issue.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="runComprehensiveTest()">🧪 Run Comprehensive Test</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Fixed formatTime function (using UTC methods)
        function formatTimeFixed(date) {
            const hours = date.getUTCHours();
            const minutes = date.getUTCMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const hour12 = hours % 12 || 12;
            return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }

        // Simulate background.js event transformation
        function transformGoogleEvent(googleApiEvent) {
            const isAllDay = !googleApiEvent.start.dateTime;
            let startDate, endDate;

            if (isAllDay) {
                startDate = new Date(googleApiEvent.start.date + 'T00:00:00Z');
                endDate = googleApiEvent.end.date ? new Date(googleApiEvent.end.date + 'T00:00:00Z') : startDate;
            } else {
                startDate = new Date(googleApiEvent.start.dateTime);
                endDate = googleApiEvent.end.dateTime ? new Date(googleApiEvent.end.dateTime) : startDate;
            }

            return {
                id: `google_${googleApiEvent.id}`,
                title: googleApiEvent.summary || 'Untitled Event',
                date: startDate.toISOString(),
                endTime: isAllDay ? null : endDate.toISOString(),
                isFullDay: isAllDay,
                isGoogleEvent: true,
                googleEventId: googleApiEvent.id
            };
        }

        // Simulate calendar.js formatDate function
        function formatDate(date) {
            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Simulate calendar cell matching logic
        function findMatchingCell(event, testCells) {
            const eventDate = new Date(event.date);
            const dateStr = formatDate(eventDate);
            const hour = eventDate.getUTCHours();
            const minutes = eventDate.getUTCMinutes();

            const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
            const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);

            return testCells.find(cell => 
                cell.dateStr === dateStr &&
                cell.utcHour === adjustedHour &&
                cell.utcMinutes === roundedMinutes
            );
        }

        function runComprehensiveTest() {
            try {
                addResult('🧪 Running Comprehensive Timezone Fix Test', 'Testing the complete fix...', 'info');

                const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                const timezoneOffset = new Date().getTimezoneOffset();

                addResult('🌍 Environment Info', `
User timezone: ${userTimezone}
Timezone offset: ${timezoneOffset} minutes (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})
Current time: ${new Date().toISOString()}
                `, 'info');

                console.log('Starting comprehensive test...');

            // Create test calendar cells for January 15, 2024
            const testCells = [];
            const testDate = new Date(2024, 0, 15); // January 15, 2024
            
            for (let hour = 0; hour < 24; hour++) {
                for (let minutes = 0; minutes < 60; minutes += 30) {
                    const cellDate = new Date(Date.UTC(2024, 0, 15, hour, minutes, 0, 0));
                    testCells.push({
                        dateStr: formatDate(cellDate),
                        utcHour: cellDate.getUTCHours(),
                        utcMinutes: cellDate.getUTCMinutes(),
                        dataTime: cellDate.toISOString()
                    });
                }
            }

            // Test various Google Calendar events
            const testEvents = [
                {
                    id: 'test1',
                    summary: '9:00 AM PST Event',
                    start: { dateTime: '2024-01-15T09:00:00-08:00' },
                    expectedUTC: '17:00',
                    expectedDisplay: '5:00 PM'
                },
                {
                    id: 'test2',
                    summary: '2:30 PM EST Event',
                    start: { dateTime: '2024-01-15T14:30:00-05:00' },
                    expectedUTC: '19:30',
                    expectedDisplay: '7:30 PM'
                },
                {
                    id: 'test3',
                    summary: '10:00 AM UTC Event',
                    start: { dateTime: '2024-01-15T10:00:00Z' },
                    expectedUTC: '10:00',
                    expectedDisplay: '10:00 AM'
                },
                {
                    id: 'test4',
                    summary: 'All Day Event',
                    start: { date: '2024-01-15' },
                    expectedUTC: '00:00',
                    expectedDisplay: 'All Day'
                }
            ];

            let passCount = 0;
            let totalTests = testEvents.length;

            testEvents.forEach((googleApiEvent, index) => {
                try {
                    console.log(`Processing test ${index + 1}: ${googleApiEvent.summary}`);

                    // Step 1: Background transformation
                    const backgroundEvent = transformGoogleEvent(googleApiEvent);
                    console.log('Background event:', backgroundEvent);

                    // Step 2: Frontend deserialization
                    const frontendEvent = {
                        ...backgroundEvent,
                        date: new Date(backgroundEvent.date)
                    };
                    console.log('Frontend event:', frontendEvent);

                    // Step 3: Calendar processing
                    const eventDate = new Date(frontendEvent.date);
                    const utcTime = `${eventDate.getUTCHours()}:${eventDate.getUTCMinutes().toString().padStart(2, '0')}`;
                    const displayTime = frontendEvent.isFullDay ? 'All Day' : formatTimeFixed(eventDate);
                    console.log('UTC time:', utcTime, 'Display time:', displayTime);

                    // Step 4: Cell matching (for timed events)
                    let cellMatched = true;
                    let matchingCell = null;
                    if (!frontendEvent.isFullDay) {
                        matchingCell = findMatchingCell(frontendEvent, testCells);
                        cellMatched = !!matchingCell;
                        console.log('Cell matched:', cellMatched, matchingCell);
                    }

                    // Step 5: Validation
                    const utcCorrect = utcTime === googleApiEvent.expectedUTC;
                    const displayCorrect = displayTime.startsWith(googleApiEvent.expectedDisplay);
                    const testPassed = utcCorrect && displayCorrect && cellMatched;
                    console.log('Test passed:', testPassed, {utcCorrect, displayCorrect, cellMatched});

                    if (testPassed) passCount++;

                    const resultClass = testPassed ? 'pass' : 'fail';
                    const resultIcon = testPassed ? '✅' : '❌';

                    addResult(`${resultIcon} Test ${index + 1}: ${googleApiEvent.summary}`, `
<div class="test-result ${resultClass}">
<strong>Original API:</strong> ${googleApiEvent.start.dateTime || googleApiEvent.start.date}<br>
<strong>Background stored:</strong> ${backgroundEvent.date}<br>
<strong>Frontend parsed:</strong> ${frontendEvent.date.toISOString()}<br>
<strong>UTC time:</strong> ${utcTime} (expected: ${googleApiEvent.expectedUTC}) ${utcCorrect ? '✅' : '❌'}<br>
<strong>Display time:</strong> ${displayTime} (expected: ${googleApiEvent.expectedDisplay}) ${displayCorrect ? '✅' : '❌'}<br>
<strong>Cell matching:</strong> ${cellMatched ? 'Found' : 'Not found'} ${cellMatched ? '✅' : '❌'}<br>
${matchingCell ? `<strong>Matched cell:</strong> ${matchingCell.dataTime}` : ''}
</div>
                    `, testPassed ? 'success' : 'error');
                } catch (testError) {
                    console.error(`Error in test ${index + 1}:`, testError);
                    addResult(`❌ Test ${index + 1} Error: ${googleApiEvent.summary}`, `
Error processing test: ${testError.message}
Stack: ${testError.stack}
                    `, 'error');
                }
            });

                console.log('Test completed. Pass count:', passCount, 'Total tests:', totalTests);

                const overallResult = passCount === totalTests ? 'success' : 'error';
                const overallIcon = passCount === totalTests ? '✅' : '❌';

                addResult(`${overallIcon} Comprehensive Test Results`, `
Tests passed: ${passCount}/${totalTests}
${passCount === totalTests ?
    '🎉 ALL TESTS PASSED! The timezone fix is working correctly.' :
    '⚠️ Some tests failed. The timezone fix may need additional work.'}

Key fixes verified:
✅ Background.js stores events in UTC correctly
✅ Frontend deserializes events correctly
✅ formatTime function uses UTC methods
✅ Calendar cell matching works with UTC times
✅ Events display at correct times
                `, overallResult);

            } catch (error) {
                console.error('Error in runComprehensiveTest:', error);
                addResult('❌ Test Error', `
An error occurred while running the test:
${error.message}

Stack trace:
${error.stack}
                `, 'error');
            }
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to verify the timezone fix. Click "Run Comprehensive Test" to verify all fixes.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
