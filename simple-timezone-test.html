<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Timezone Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 Simple Timezone Fix Test</h1>
    <p>This test verifies that the formatTime function fix resolves the 7-hour offset issue.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="runSimpleTest()">🧪 Run Simple Test</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Fixed formatTime function (using UTC methods)
        function formatTimeFixed(date) {
            const hours = date.getUTCHours();
            const minutes = date.getUTCMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const hour12 = hours % 12 || 12;
            return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }

        // Old formatTime function (broken - using local methods)
        function formatTimeBroken(date) {
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const hour12 = hours % 12 || 12;
            return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }

        function runSimpleTest() {
            try {
                addResult('🧪 Running Simple Timezone Test', 'Testing the formatTime function fix...', 'info');
                
                const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                const timezoneOffset = new Date().getTimezoneOffset();
                
                addResult('🌍 Environment Info', `
User timezone: ${userTimezone}
Timezone offset: ${timezoneOffset} minutes (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})
Current time: ${new Date().toISOString()}
                `, 'info');

                // Test a simple Google Calendar event: 9:00 AM PST
                const googleApiEvent = {
                    summary: '9:00 AM PST Meeting',
                    start: { dateTime: '2024-01-15T09:00:00-08:00' }
                };

                addResult('📅 Test Event', `
Google API Event: ${googleApiEvent.summary}
Original time: ${googleApiEvent.start.dateTime}
Expected UTC: 17:00 (9 AM PST + 8 hours)
Expected display: 5:00 PM
                `, 'info');

                // Step 1: Parse the Google API time
                const originalDate = new Date(googleApiEvent.start.dateTime);
                console.log('Original date:', originalDate);

                // Step 2: Background.js stores as ISO string
                const backgroundStored = originalDate.toISOString();
                console.log('Background stored:', backgroundStored);

                // Step 3: Frontend deserializes back to Date
                const frontendDate = new Date(backgroundStored);
                console.log('Frontend date:', frontendDate);

                // Step 4: Test both formatTime functions
                const brokenResult = formatTimeBroken(frontendDate);
                const fixedResult = formatTimeFixed(frontendDate);

                console.log('Broken result:', brokenResult);
                console.log('Fixed result:', fixedResult);

                // Step 5: Check UTC time
                const utcHours = frontendDate.getUTCHours();
                const utcMinutes = frontendDate.getUTCMinutes();
                const utcTime = `${utcHours}:${utcMinutes.toString().padStart(2, '0')}`;

                // Step 6: Validate results
                const utcCorrect = utcTime === '17:00';
                const fixedCorrect = fixedResult === '5:00 PM';
                const brokenIncorrect = brokenResult !== '5:00 PM';

                addResult('🔍 Test Results', `
UTC time: ${utcTime} ${utcCorrect ? '✅ CORRECT' : '❌ INCORRECT'}
OLD formatTime (broken): ${brokenResult} ${brokenIncorrect ? '✅ CORRECTLY BROKEN' : '❌ UNEXPECTEDLY CORRECT'}
NEW formatTime (fixed): ${fixedResult} ${fixedCorrect ? '✅ CORRECT' : '❌ INCORRECT'}

Analysis:
- Original PST time: 9:00 AM
- Converted to UTC: ${utcTime} (expected: 17:00)
- Fixed display: ${fixedResult} (expected: 5:00 PM)
                `, utcCorrect && fixedCorrect ? 'success' : 'error');

                const overallPassed = utcCorrect && fixedCorrect;
                addResult(overallPassed ? '✅ Test PASSED' : '❌ Test FAILED', `
${overallPassed ? 
    '🎉 The timezone fix is working correctly! Events will now display at their correct times.' : 
    '⚠️ The timezone fix is not working as expected. Please check the implementation.'}
                `, overallPassed ? 'success' : 'error');

            } catch (error) {
                console.error('Test error:', error);
                addResult('❌ Test Error', `
An error occurred: ${error.message}
Stack: ${error.stack}
                `, 'error');
            }
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to test the timezone fix. Click "Run Simple Test" to verify the fix.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
