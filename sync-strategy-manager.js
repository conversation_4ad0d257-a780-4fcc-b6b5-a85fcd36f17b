/**
 * Sync Strategy Manager for CalenTask
 * Implements intelligent sync strategies with conflict resolution
 */
class SyncStrategyManager {
  constructor(storageManager, authService) {
    this.storageManager = storageManager;
    this.authService = authService;
    this.syncInProgress = false;
    this.syncQueue = [];
    this.retryAttempts = new Map();
    this.maxRetries = 3;
    this.syncStrategies = {
      IMMEDIATE: 'immediate',
      DEBOUNCED: 'debounced',
      SCHEDULED: 'scheduled',
      ON_DEMAND: 'on_demand'
    };

    this.currentStrategy = this.syncStrategies.DEBOUNCED;
    this.debounceTimeout = null;
    this.debounceDelay = 2000; // 2 seconds

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for automatic sync triggers
   */
  setupEventListeners() {
    // Listen for network status changes
    if (window.navigator && window.navigator.onLine !== undefined) {
      window.addEventListener('online', () => {
        console.log('🌐 Network connection restored, triggering sync');
        this.triggerSync('network_restored');
      });

      window.addEventListener('offline', () => {
        console.log('📴 Network connection lost');
      });
    }

    // Listen for visibility changes (tab focus/blur)
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        console.log('👁️ Tab became visible, checking for sync');
        this.checkAndSync('tab_visible');
      }
    });
  }

  /**
   * Set sync strategy
   */
  setSyncStrategy(strategy) {
    if (Object.values(this.syncStrategies).includes(strategy)) {
      this.currentStrategy = strategy;
      console.log(`🔄 Sync strategy changed to: ${strategy}`);
    } else {
      console.warn(`⚠️ Invalid sync strategy: ${strategy}`);
    }
  }

  /**
   * Trigger sync based on current strategy
   */
  async triggerSync(reason = 'manual', options = {}) {
    console.log(`🚀 Sync triggered: ${reason}`);

    switch (this.currentStrategy) {
      case this.syncStrategies.IMMEDIATE:
        return await this.performSync(reason, options);

      case this.syncStrategies.DEBOUNCED:
        return this.debouncedSync(reason, options);

      case this.syncStrategies.SCHEDULED:
        return this.scheduleSync(reason, options);

      case this.syncStrategies.ON_DEMAND:
        console.log('📋 On-demand strategy: sync queued for manual execution');
        this.queueSync(reason, options);
        return { queued: true, reason };

      default:
        return await this.performSync(reason, options);
    }
  }

  /**
   * Debounced sync - waits for a pause in sync requests
   */
  debouncedSync(reason, options) {
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }

    this.debounceTimeout = setTimeout(async () => {
      await this.performSync(reason, options);
    }, this.debounceDelay);

    return { debounced: true, reason, delay: this.debounceDelay };
  }

  /**
   * Schedule sync for later execution
   */
  scheduleSync(reason, options, delay = 5000) {
    setTimeout(async () => {
      await this.performSync(reason, options);
    }, delay);

    return { scheduled: true, reason, delay };
  }

  /**
   * Queue sync for manual execution
   */
  queueSync(reason, options) {
    this.syncQueue.push({ reason, options, timestamp: Date.now() });
    console.log(`📋 Sync queued: ${reason} (queue length: ${this.syncQueue.length})`);
  }

  /**
   * Execute all queued syncs
   */
  async executeQueuedSyncs() {
    if (this.syncQueue.length === 0) {
      console.log('📋 No queued syncs to execute');
      return { executed: 0 };
    }

    const queueLength = this.syncQueue.length;
    console.log(`🔄 Executing ${queueLength} queued syncs`);

    const results = [];
    while (this.syncQueue.length > 0) {
      const syncItem = this.syncQueue.shift();
      try {
        const result = await this.performSync(syncItem.reason, syncItem.options);
        results.push({ success: true, result, item: syncItem });
      } catch (error) {
        results.push({ success: false, error, item: syncItem });
      }
    }

    return { executed: queueLength, results };
  }

  /**
   * Check if sync is needed and perform it
   */
  async checkAndSync(reason = 'check') {
    const shouldSync = await this.shouldPerformSync();

    if (shouldSync.should) {
      return await this.triggerSync(`${reason}_${shouldSync.reason}`, shouldSync.options);
    } else {
      console.log(`⏭️ Sync skipped: ${shouldSync.reason}`);
      return { skipped: true, reason: shouldSync.reason };
    }
  }

  /**
   * Determine if sync should be performed
   */
  async shouldPerformSync() {
    // Check if already syncing
    if (this.syncInProgress) {
      return { should: false, reason: 'sync_in_progress' };
    }

    // Check authentication
    if (!this.authService || !this.authService.isAuthenticated) {
      return { should: false, reason: 'not_authenticated' };
    }

    // Check network connectivity
    if (window.navigator && !window.navigator.onLine) {
      return { should: false, reason: 'offline' };
    }

    // Check last sync time
    const { lastSync } = await this.storageManager.loadGoogleEvents();
    if (lastSync) {
      const timeSinceLastSync = Date.now() - new Date(lastSync).getTime();
      const minSyncInterval = 2 * 60 * 1000; // 2 minutes

      if (timeSinceLastSync < minSyncInterval) {
        return {
          should: false,
          reason: 'recent_sync',
          timeSinceLastSync,
          minInterval: minSyncInterval
        };
      }
    }

    return { should: true, reason: 'ready' };
  }

  /**
   * Perform the actual sync operation
   */
  async performSync(reason = 'manual', options = {}) {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress, skipping');
      return { skipped: true, reason: 'sync_in_progress' };
    }

    this.syncInProgress = true;
    const syncStartTime = Date.now();

    try {
      console.log(`🔄 Starting sync: ${reason}`);

      // Load current local data
      const localData = await this.storageManager.loadGoogleEvents();

      // Perform sync with retry logic
      const syncResult = await this.syncWithRetry(reason, options);

      // Calculate sync duration
      const syncDuration = Date.now() - syncStartTime;

      const result = {
        success: true,
        reason,
        duration: syncDuration,
        timestamp: new Date().toISOString(),
        ...syncResult
      };

      console.log(`✅ Sync completed: ${reason} (${syncDuration}ms)`);
      return result;

    } catch (error) {
      const syncDuration = Date.now() - syncStartTime;
      console.error(`❌ Sync failed: ${reason} (${syncDuration}ms)`, error);

      return {
        success: false,
        reason,
        duration: syncDuration,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Sync with retry logic
   */
  async syncWithRetry(reason, options, attempt = 1) {
    const retryKey = `${reason}_${Date.now()}`;

    try {
      // Perform the actual Google Calendar sync
      const result = await this.performGoogleCalendarSync();

      // Clear retry attempts on success
      this.retryAttempts.delete(retryKey);

      return result;
    } catch (error) {
      if (attempt < this.maxRetries) {
        const retryDelay = Math.pow(2, attempt) * 1000; // Exponential backoff
        console.log(`🔄 Sync attempt ${attempt} failed, retrying in ${retryDelay}ms`);

        this.retryAttempts.set(retryKey, attempt);

        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await this.syncWithRetry(reason, options, attempt + 1);
      } else {
        console.error(`❌ Sync failed after ${this.maxRetries} attempts`);
        this.retryAttempts.delete(retryKey);
        throw error;
      }
    }
  }

  /**
   * Perform actual Google Calendar sync
   */
  async performGoogleCalendarSync() {
    // Check if we have a Google Calendar service to work with
    if (!this.authService || !this.authService.googleCalendarService) {
      // Try to find the service from the global scope
      if (window.googleCalendarService) {
        return await window.googleCalendarService.syncCalendarEvents();
      }
      throw new Error('Google Calendar service not available');
    }

    return await this.authService.googleCalendarService.syncCalendarEvents();
  }

  /**
   * Get sync status and statistics
   */
  getSyncStatus() {
    return {
      strategy: this.currentStrategy,
      inProgress: this.syncInProgress,
      queueLength: this.syncQueue.length,
      retryAttempts: this.retryAttempts.size,
      isOnline: window.navigator ? window.navigator.onLine : true
    };
  }

  /**
   * Clear all sync state
   */
  clearSyncState() {
    this.syncQueue = [];
    this.retryAttempts.clear();
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
      this.debounceTimeout = null;
    }
    console.log('🗑️ Sync state cleared');
  }
}

// Export to global scope
window.SyncStrategyManager = SyncStrategyManager;
