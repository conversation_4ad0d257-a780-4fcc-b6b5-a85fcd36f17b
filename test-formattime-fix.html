<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormatTime Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
    </style>
</head>
<body>
    <h1>🔧 FormatTime Function Fix Test</h1>
    <p>This test verifies that the formatTime function fix resolves the 7-hour offset issue.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="runFormatTimeTest()">🧪 Run FormatTime Test</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Old formatTime function (broken)
        function formatTimeBroken(date) {
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const hour12 = hours % 12 || 12;
            return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }

        // New formatTime function (fixed)
        function formatTimeFixed(date) {
            const hours = date.getUTCHours();
            const minutes = date.getUTCMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const hour12 = hours % 12 || 12;
            return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
        }

        function runFormatTimeTest() {
            addResult('🧪 Running FormatTime Fix Test', 'Testing the formatTime function fix...', 'info');
            
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const timezoneOffset = new Date().getTimezoneOffset();
            
            addResult('🌍 Environment Info', `
User timezone: ${userTimezone}
Timezone offset: ${timezoneOffset} minutes (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})
Current time: ${new Date().toISOString()}
            `, 'info');
            
            // Test various Google Calendar events
            const testEvents = [
                {
                    name: '9:00 AM PST Event',
                    googleApiTime: '2024-01-15T09:00:00-08:00',
                    expectedDisplay: '5:00 PM' // 9 AM PST = 5 PM UTC
                },
                {
                    name: '2:00 PM EST Event',
                    googleApiTime: '2024-01-16T14:00:00-05:00',
                    expectedDisplay: '7:00 PM' // 2 PM EST = 7 PM UTC
                },
                {
                    name: '10:30 AM UTC Event',
                    googleApiTime: '2024-01-17T10:30:00Z',
                    expectedDisplay: '10:30 AM' // 10:30 AM UTC = 10:30 AM UTC
                },
                {
                    name: '11:45 PM PST Event',
                    googleApiTime: '2024-01-18T23:45:00-08:00',
                    expectedDisplay: '7:45 AM' // 11:45 PM PST = 7:45 AM UTC next day
                }
            ];
            
            let passCount = 0;
            let totalTests = testEvents.length;
            
            testEvents.forEach((testEvent, index) => {
                // Simulate the background.js transformation
                const eventDate = new Date(testEvent.googleApiTime);
                const backgroundEvent = {
                    date: eventDate.toISOString(),
                    title: testEvent.name
                };
                
                // Simulate frontend deserialization
                const frontendDate = new Date(backgroundEvent.date);
                
                // Test both formatTime functions
                const brokenResult = formatTimeBroken(frontendDate);
                const fixedResult = formatTimeFixed(frontendDate);
                
                const testPassed = fixedResult.startsWith(testEvent.expectedDisplay);
                if (testPassed) passCount++;
                
                const resultClass = testPassed ? 'success' : 'error';
                const resultIcon = testPassed ? '✅' : '❌';
                
                addResult(`${resultIcon} Test ${index + 1}: ${testEvent.name}`, `
Original API time: ${testEvent.googleApiTime}
Expected display: ${testEvent.expectedDisplay}
UTC time stored: ${frontendDate.toISOString()}

OLD formatTime (broken): ${brokenResult}
NEW formatTime (fixed): ${fixedResult}

Test result: ${testPassed ? 'PASS' : 'FAIL'}
                `, resultClass);
            });
            
            const overallResult = passCount === totalTests ? 'success' : 'error';
            const overallIcon = passCount === totalTests ? '✅' : '❌';
            
            addResult(`${overallIcon} FormatTime Test Results`, `
Tests passed: ${passCount}/${totalTests}
${passCount === totalTests ? 
    '🎉 ALL TESTS PASSED! The formatTime fix is working correctly.' : 
    '⚠️ Some tests failed. The formatTime fix may need additional work.'}
            `, overallResult);
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to test the formatTime fix. Click "Run FormatTime Test" to verify the fix.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
