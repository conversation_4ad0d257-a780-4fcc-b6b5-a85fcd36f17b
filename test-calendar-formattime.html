<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Calendar FormatTime</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
    </style>
</head>
<body>
    <h1>🔧 Test Calendar FormatTime Function</h1>
    <p>This test loads the actual calendar.js file and tests the formatTime function.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="testCalendarFormatTime()">🧪 Test Calendar FormatTime</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <!-- Load the actual calendar.js file -->
    <script src="calendar.js"></script>
    
    <script>
        function testCalendarFormatTime() {
            try {
                addResult('🧪 Testing Calendar FormatTime Function', 'Testing the actual calendar.js formatTime function...', 'info');
                
                // Create a dummy calendar instance to access the formatTime method
                const dummyContainer = document.createElement('div');
                const calendar = new SimpleCalendar(dummyContainer);
                
                addResult('✅ Calendar Instance Created', 'Successfully created SimpleCalendar instance', 'success');
                
                // Test the formatTime function with a known UTC time
                // 9:00 AM PST = 17:00 UTC
                const testDate = new Date('2024-01-15T17:00:00Z'); // 5:00 PM UTC
                
                addResult('📅 Test Date', `
Test date: ${testDate.toISOString()}
UTC time: ${testDate.getUTCHours()}:${testDate.getUTCMinutes().toString().padStart(2, '0')}
Local time: ${testDate.getHours()}:${testDate.getMinutes().toString().padStart(2, '0')}
                `, 'info');
                
                // Call the formatTime function
                const formattedTime = calendar.formatTime(testDate);
                
                addResult('🔍 FormatTime Result', `
Formatted time: ${formattedTime}
Expected: 5:00 PM
Test passed: ${formattedTime === '5:00 PM' ? '✅ YES' : '❌ NO'}
                `, formattedTime === '5:00 PM' ? 'success' : 'error');
                
                // Test multiple times to be thorough
                const testCases = [
                    { utc: '2024-01-15T09:00:00Z', expected: '9:00 AM' },
                    { utc: '2024-01-15T17:00:00Z', expected: '5:00 PM' },
                    { utc: '2024-01-15T12:30:00Z', expected: '12:30 PM' },
                    { utc: '2024-01-15T00:00:00Z', expected: '12:00 AM' },
                    { utc: '2024-01-15T23:45:00Z', expected: '11:45 PM' }
                ];
                
                let passCount = 0;
                let totalTests = testCases.length;
                
                testCases.forEach((testCase, index) => {
                    const date = new Date(testCase.utc);
                    const result = calendar.formatTime(date);
                    const passed = result === testCase.expected;
                    
                    if (passed) passCount++;
                    
                    addResult(`Test ${index + 1}: ${testCase.utc}`, `
UTC input: ${testCase.utc}
Expected: ${testCase.expected}
Actual: ${result}
Result: ${passed ? '✅ PASS' : '❌ FAIL'}
                    `, passed ? 'success' : 'error');
                });
                
                addResult(`📊 Overall Results`, `
Tests passed: ${passCount}/${totalTests}
${passCount === totalTests ? 
    '🎉 ALL TESTS PASSED! The formatTime function is working correctly.' : 
    '⚠️ Some tests failed. The formatTime function may need additional work.'}
                `, passCount === totalTests ? 'success' : 'error');
                
            } catch (error) {
                console.error('Test error:', error);
                addResult('❌ Test Error', `
An error occurred: ${error.message}
Stack: ${error.stack}

This might indicate that calendar.js couldn't be loaded or the SimpleCalendar class is not available.
                `, 'error');
            }
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to test the calendar formatTime function. Click "Test Calendar FormatTime" to run the test.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
