/**
 * Local Storage Manager for CalenTask
 * Handles all local data persistence with enhanced features
 */
class LocalStorageManager {
  constructor() {
    this.storageKeys = {
      GOOGLE_EVENTS: 'google_calendar_events',
      GOOGLE_METADATA: 'google_calendar_sync_metadata',
      GOOGLE_LAST_SYNC: 'google_calendar_last_sync',
      GOOGLE_ENABLED: 'google_calendar_enabled',
      TASKS: 'tasks',
      ARCHIVED_TASKS: 'archivedTasks',
      SYNC_QUEUE: 'sync_queue',
      OFFLINE_CHANGES: 'offline_changes'
    };
    
    this.eventListeners = new Map();
    this.setupStorageListener();
  }

  /**
   * Setup storage change listener
   */
  setupStorageListener() {
    if (chrome && chrome.storage && chrome.storage.onChanged) {
      chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
          Object.keys(changes).forEach(key => {
            if (this.eventListeners.has(key)) {
              this.eventListeners.get(key).forEach(callback => {
                callback(changes[key].newValue, changes[key].oldValue);
              });
            }
          });
        }
      });
    }
  }

  /**
   * Subscribe to storage changes for a specific key
   */
  subscribe(key, callback) {
    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, new Set());
    }
    this.eventListeners.get(key).add(callback);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(key);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.eventListeners.delete(key);
        }
      }
    };
  }

  /**
   * Save Google Calendar events with metadata
   */
  async saveGoogleEvents(events, metadata = {}) {
    try {
      const serializedEvents = this.serializeEvents(events);
      const syncMetadata = {
        version: '1.0',
        eventCount: events.length,
        lastUpdateTime: new Date().toISOString(),
        syncSource: 'local-storage-manager',
        ...metadata
      };

      await chrome.storage.local.set({
        [this.storageKeys.GOOGLE_EVENTS]: serializedEvents,
        [this.storageKeys.GOOGLE_METADATA]: syncMetadata,
        [this.storageKeys.GOOGLE_LAST_SYNC]: new Date().toISOString()
      });

      console.log(`💾 LocalStorageManager: Saved ${events.length} Google Calendar events`);
      return true;
    } catch (error) {
      console.error('❌ LocalStorageManager: Failed to save Google events:', error);
      return false;
    }
  }

  /**
   * Load Google Calendar events with validation
   */
  async loadGoogleEvents() {
    try {
      const result = await chrome.storage.local.get([
        this.storageKeys.GOOGLE_EVENTS,
        this.storageKeys.GOOGLE_METADATA,
        this.storageKeys.GOOGLE_LAST_SYNC
      ]);

      const events = result[this.storageKeys.GOOGLE_EVENTS] || [];
      const metadata = result[this.storageKeys.GOOGLE_METADATA] || {};
      const lastSync = result[this.storageKeys.GOOGLE_LAST_SYNC];

      // Validate data integrity
      const deserializedEvents = this.deserializeEvents(events);
      const isValid = this.validateEventData(deserializedEvents, metadata);

      if (!isValid) {
        console.warn('⚠️ LocalStorageManager: Data integrity check failed, returning empty events');
        return { events: [], metadata: {}, lastSync: null, isValid: false };
      }

      console.log(`📦 LocalStorageManager: Loaded ${deserializedEvents.length} Google Calendar events`);
      return {
        events: deserializedEvents,
        metadata,
        lastSync,
        isValid: true
      };
    } catch (error) {
      console.error('❌ LocalStorageManager: Failed to load Google events:', error);
      return { events: [], metadata: {}, lastSync: null, isValid: false };
    }
  }

  /**
   * Validate event data integrity
   */
  validateEventData(events, metadata) {
    if (!Array.isArray(events)) {
      console.warn('Events is not an array');
      return false;
    }

    if (metadata.eventCount !== undefined && metadata.eventCount !== events.length) {
      console.warn(`Event count mismatch: expected ${metadata.eventCount}, got ${events.length}`);
      return false;
    }

    // Validate each event has required fields
    for (const event of events) {
      if (!event.id || !event.title || !event.date) {
        console.warn('Event missing required fields:', event);
        return false;
      }
      
      if (!(event.date instanceof Date) || isNaN(event.date.getTime())) {
        console.warn('Event has invalid date:', event);
        return false;
      }
    }

    return true;
  }

  /**
   * Serialize events for storage (convert Date objects to ISO strings)
   */
  serializeEvents(events) {
    return events.map(event => ({
      ...event,
      date: event.date instanceof Date ? event.date.toISOString() : event.date,
      endTime: event.endTime instanceof Date ? event.endTime.toISOString() : event.endTime,
      created: event.created instanceof Date ? event.created.toISOString() : event.created,
      updated: event.updated instanceof Date ? event.updated.toISOString() : event.updated
    }));
  }

  /**
   * Deserialize events from storage (convert ISO strings back to Date objects)
   */
  deserializeEvents(events) {
    if (!Array.isArray(events)) {
      console.warn('deserializeEvents received non-array input:', events);
      return [];
    }

    return events.map(event => ({
      ...event,
      date: typeof event.date === 'string' ? new Date(event.date) : event.date,
      endTime: event.endTime && typeof event.endTime === 'string' ? new Date(event.endTime) : event.endTime,
      created: event.created && typeof event.created === 'string' ? new Date(event.created) : event.created,
      updated: event.updated && typeof event.updated === 'string' ? new Date(event.updated) : event.updated
    }));
  }

  /**
   * Clear all Google Calendar data
   */
  async clearGoogleData() {
    try {
      await chrome.storage.local.remove([
        this.storageKeys.GOOGLE_EVENTS,
        this.storageKeys.GOOGLE_METADATA,
        this.storageKeys.GOOGLE_LAST_SYNC
      ]);
      console.log('🗑️ LocalStorageManager: Cleared Google Calendar data');
      return true;
    } catch (error) {
      console.error('❌ LocalStorageManager: Failed to clear Google data:', error);
      return false;
    }
  }

  /**
   * Get storage usage statistics
   */
  async getStorageStats() {
    try {
      const result = await chrome.storage.local.get(null);
      const stats = {
        totalKeys: Object.keys(result).length,
        googleEvents: result[this.storageKeys.GOOGLE_EVENTS]?.length || 0,
        tasks: result[this.storageKeys.TASKS]?.length || 0,
        archivedTasks: result[this.storageKeys.ARCHIVED_TASKS]?.length || 0,
        lastSync: result[this.storageKeys.GOOGLE_LAST_SYNC],
        estimatedSize: JSON.stringify(result).length
      };
      
      console.log('📊 Storage Statistics:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Failed to get storage stats:', error);
      return null;
    }
  }

  /**
   * Export all data for backup
   */
  async exportData() {
    try {
      const result = await chrome.storage.local.get(null);
      const exportData = {
        version: '1.0',
        exportTime: new Date().toISOString(),
        data: result
      };
      
      console.log('📤 Data exported successfully');
      return exportData;
    } catch (error) {
      console.error('❌ Failed to export data:', error);
      return null;
    }
  }

  /**
   * Import data from backup
   */
  async importData(importData) {
    try {
      if (!importData || !importData.data) {
        throw new Error('Invalid import data format');
      }
      
      await chrome.storage.local.clear();
      await chrome.storage.local.set(importData.data);
      
      console.log('📥 Data imported successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to import data:', error);
      return false;
    }
  }
}

// Export to global scope
window.LocalStorageManager = LocalStorageManager;
