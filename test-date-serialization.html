<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Calendar Date Serialization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Google Calendar Date Serialization Test</h1>
    <p>This test verifies that Date objects are properly serialized and deserialized when storing Google Calendar events.</p>

    <div class="test-section info">
        <h3>Test Controls</h3>
        <button onclick="runSerializationTest()">Run Serialization Test</button>
        <button onclick="runStorageTest()">Run Chrome Storage Test</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Mock Google Calendar Service for testing
        class TestGoogleCalendarService {
            constructor() {
                this.events = [];
            }

            serializeEvents(events) {
                return events.map(event => ({
                    ...event,
                    date: event.date instanceof Date ? event.date.toISOString() : event.date,
                    endTime: event.endTime instanceof Date ? event.endTime.toISOString() : event.endTime,
                    created: event.created instanceof Date ? event.created.toISOString() : event.created,
                    updated: event.updated instanceof Date ? event.updated.toISOString() : event.updated
                }));
            }

            deserializeEvents(events) {
                return events.map(event => ({
                    ...event,
                    date: typeof event.date === 'string' ? new Date(event.date) : event.date,
                    endTime: event.endTime && typeof event.endTime === 'string' ? new Date(event.endTime) : event.endTime,
                    created: event.created && typeof event.created === 'string' ? new Date(event.created) : event.created,
                    updated: event.updated && typeof event.updated === 'string' ? new Date(event.updated) : event.updated
                }));
            }
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function runSerializationTest() {
            const service = new TestGoogleCalendarService();
            
            // Create test events with Date objects
            const testEvents = [
                {
                    id: 'google_test1',
                    title: 'Test Meeting',
                    date: new Date('2024-01-15T10:00:00Z'),
                    endTime: new Date('2024-01-15T11:00:00Z'),
                    isFullDay: false,
                    isGoogleEvent: true,
                    created: new Date('2024-01-01T00:00:00Z'),
                    updated: new Date('2024-01-10T00:00:00Z')
                },
                {
                    id: 'google_test2',
                    title: 'All Day Event',
                    date: new Date('2024-01-16T00:00:00Z'),
                    endTime: null,
                    isFullDay: true,
                    isGoogleEvent: true,
                    created: new Date('2024-01-01T00:00:00Z'),
                    updated: null
                }
            ];

            // Test serialization
            const serialized = service.serializeEvents(testEvents);
            
            // Test deserialization
            const deserialized = service.deserializeEvents(serialized);

            let result = 'SERIALIZATION TEST RESULTS:\n\n';
            result += '1. Original Events:\n';
            result += JSON.stringify(testEvents.map(e => ({
                ...e,
                date: e.date.toISOString(),
                endTime: e.endTime ? e.endTime.toISOString() : null,
                created: e.created ? e.created.toISOString() : null,
                updated: e.updated ? e.updated.toISOString() : null
            })), null, 2);
            
            result += '\n\n2. Serialized Events (for storage):\n';
            result += JSON.stringify(serialized, null, 2);
            
            result += '\n\n3. Deserialized Events (after loading):\n';
            result += JSON.stringify(deserialized.map(e => ({
                ...e,
                date: e.date instanceof Date ? e.date.toISOString() : e.date,
                endTime: e.endTime instanceof Date ? e.endTime.toISOString() : e.endTime,
                created: e.created instanceof Date ? e.created.toISOString() : e.created,
                updated: e.updated instanceof Date ? e.updated.toISOString() : e.updated
            })), null, 2);

            // Verify dates are properly restored
            let success = true;
            let errors = [];

            deserialized.forEach((event, index) => {
                if (!(event.date instanceof Date)) {
                    success = false;
                    errors.push(`Event ${index + 1}: date is not a Date object`);
                }
                if (event.endTime && !(event.endTime instanceof Date)) {
                    success = false;
                    errors.push(`Event ${index + 1}: endTime is not a Date object`);
                }
                if (event.created && !(event.created instanceof Date)) {
                    success = false;
                    errors.push(`Event ${index + 1}: created is not a Date object`);
                }
            });

            result += '\n\n4. Validation Results:\n';
            if (success) {
                result += '✅ All Date objects properly restored!';
                addResult('Serialization Test', result, 'success');
            } else {
                result += '❌ Errors found:\n' + errors.join('\n');
                addResult('Serialization Test', result, 'error');
            }
        }

        function runStorageTest() {
            if (!chrome || !chrome.storage) {
                addResult('Chrome Storage Test', '❌ Chrome storage API not available (run in extension context)', 'error');
                return;
            }

            const service = new TestGoogleCalendarService();
            const testEvents = [
                {
                    id: 'storage_test',
                    title: 'Storage Test Event',
                    date: new Date(),
                    endTime: new Date(Date.now() + 3600000), // 1 hour later
                    isFullDay: false,
                    isGoogleEvent: true
                }
            ];

            // Serialize and store
            const serialized = service.serializeEvents(testEvents);
            
            chrome.storage.local.set({ test_events: serialized }, () => {
                // Retrieve and deserialize
                chrome.storage.local.get(['test_events'], (result) => {
                    const retrieved = result.test_events || [];
                    const deserialized = service.deserializeEvents(retrieved);

                    let testResult = 'CHROME STORAGE TEST RESULTS:\n\n';
                    testResult += '1. Stored events:\n';
                    testResult += JSON.stringify(serialized, null, 2);
                    testResult += '\n\n2. Retrieved events:\n';
                    testResult += JSON.stringify(retrieved, null, 2);
                    testResult += '\n\n3. Deserialized events:\n';
                    testResult += JSON.stringify(deserialized.map(e => ({
                        ...e,
                        date: e.date instanceof Date ? e.date.toISOString() : e.date,
                        endTime: e.endTime instanceof Date ? e.endTime.toISOString() : e.endTime
                    })), null, 2);

                    const success = deserialized.length > 0 && 
                                  deserialized[0].date instanceof Date &&
                                  deserialized[0].endTime instanceof Date;

                    testResult += '\n\n4. Validation:\n';
                    testResult += success ? '✅ Chrome storage test passed!' : '❌ Chrome storage test failed!';

                    addResult('Chrome Storage Test', testResult, success ? 'success' : 'error');

                    // Clean up
                    chrome.storage.local.remove(['test_events']);
                });
            });
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
