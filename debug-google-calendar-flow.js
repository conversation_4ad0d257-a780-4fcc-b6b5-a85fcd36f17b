/**
 * Debug script for Google Calendar data flow
 * Add this to your extension's popup or content script to debug the complete flow
 */

// Debug function to trace the complete Google Calendar data flow
async function debugGoogleCalendarFlow() {
    console.log('🔍 === DEBUGGING GOOGLE CALENDAR DATA FLOW ===');
    
    // Step 1: Check Chrome storage directly
    console.log('\n📦 Step 1: Checking Chrome storage...');
    try {
        const storageData = await chrome.storage.local.get([
            'google_calendar_enabled',
            'google_calendar_events',
            'google_calendar_last_sync'
        ]);
        
        console.log('Storage data:', {
            enabled: storageData.google_calendar_enabled,
            eventCount: storageData.google_calendar_events ? storageData.google_calendar_events.length : 0,
            lastSync: storageData.google_calendar_last_sync,
            rawEvents: storageData.google_calendar_events
        });
        
        if (storageData.google_calendar_events && storageData.google_calendar_events.length > 0) {
            console.log('Sample stored event:', storageData.google_calendar_events[0]);
        }
    } catch (error) {
        console.error('❌ Error reading storage:', error);
    }
    
    // Step 2: Check service availability
    console.log('\n🔧 Step 2: Checking service availability...');
    if (typeof googleCalendarService === 'undefined') {
        console.error('❌ googleCalendarService is not defined');
        return;
    }
    
    console.log('✅ googleCalendarService is available');
    
    // Step 3: Check service initialization
    console.log('\n⏳ Step 3: Checking service initialization...');
    console.log('Service initialization status:', {
        isInitialized: googleCalendarService.isServiceInitialized(),
        isEnabled: googleCalendarService.isEnabled,
        isReady: googleCalendarService.isReady(),
        eventCount: googleCalendarService.events ? googleCalendarService.events.length : 0
    });
    
    if (!googleCalendarService.isServiceInitialized()) {
        console.log('⏳ Waiting for service initialization...');
        await googleCalendarService.waitForInitialization();
        console.log('✅ Service initialization completed');
    }
    
    // Step 4: Check events in service
    console.log('\n📋 Step 4: Checking events in service...');
    const serviceEvents = googleCalendarService.getEvents();
    console.log('Events from service:', {
        count: serviceEvents ? serviceEvents.length : 0,
        events: serviceEvents
    });
    
    if (serviceEvents && serviceEvents.length > 0) {
        console.log('Sample service event:', {
            title: serviceEvents[0].title,
            date: serviceEvents[0].date,
            dateType: typeof serviceEvents[0].date,
            isValidDate: serviceEvents[0].date instanceof Date,
            isFullDay: serviceEvents[0].isFullDay
        });
    }
    
    // Step 5: Check calendar availability
    console.log('\n📅 Step 5: Checking calendar availability...');
    if (typeof calendar === 'undefined') {
        console.error('❌ calendar is not defined');
        return;
    }
    
    console.log('✅ calendar is available');
    console.log('Calendar Google events:', {
        count: calendar.googleEvents ? calendar.googleEvents.length : 0,
        events: calendar.googleEvents
    });
    
    // Step 6: Test manual update
    console.log('\n🔄 Step 6: Testing manual calendar update...');
    try {
        updateCalendarWithGoogleEvents();
        console.log('✅ Manual update completed');
        
        // Check if events were added to calendar
        console.log('Calendar Google events after update:', {
            count: calendar.googleEvents ? calendar.googleEvents.length : 0
        });
    } catch (error) {
        console.error('❌ Error during manual update:', error);
    }
    
    // Step 7: Test serialization/deserialization
    console.log('\n🔄 Step 7: Testing serialization/deserialization...');
    if (googleCalendarService.events && googleCalendarService.events.length > 0) {
        const testEvent = googleCalendarService.events[0];
        console.log('Original event:', testEvent);
        
        const serialized = googleCalendarService.serializeEvents([testEvent]);
        console.log('Serialized event:', serialized[0]);
        
        const deserialized = googleCalendarService.deserializeEvents(serialized);
        console.log('Deserialized event:', deserialized[0]);
        
        console.log('Date type check:', {
            original: testEvent.date instanceof Date,
            serialized: typeof serialized[0].date,
            deserialized: deserialized[0].date instanceof Date
        });
    }
    
    console.log('\n✅ === DEBUG COMPLETE ===');
}

// Function to manually trigger cached event loading
async function manuallyLoadCachedEvents() {
    console.log('🔄 Manually loading cached events...');
    
    if (!googleCalendarService) {
        console.error('❌ Google Calendar service not available');
        return;
    }
    
    await googleCalendarService.waitForInitialization();
    
    const cachedEvents = googleCalendarService.loadCachedEvents();
    console.log('Cached events loaded:', cachedEvents ? cachedEvents.length : 0);
    
    if (calendar) {
        updateCalendarWithGoogleEvents();
        console.log('✅ Calendar updated with cached events');
    }
}

// Function to clear all Google Calendar data (for testing)
async function clearGoogleCalendarData() {
    console.log('🗑️ Clearing Google Calendar data...');
    
    try {
        await chrome.storage.local.remove([
            'google_calendar_enabled',
            'google_calendar_events',
            'google_calendar_last_sync'
        ]);
        
        if (googleCalendarService) {
            googleCalendarService.events = [];
            googleCalendarService.isEnabled = false;
            googleCalendarService.lastSyncTime = null;
        }
        
        if (calendar) {
            calendar.setGoogleEvents([]);
        }
        
        console.log('✅ Google Calendar data cleared');
    } catch (error) {
        console.error('❌ Error clearing data:', error);
    }
}

// Export functions for console use
window.debugGoogleCalendarFlow = debugGoogleCalendarFlow;
window.manuallyLoadCachedEvents = manuallyLoadCachedEvents;
window.clearGoogleCalendarData = clearGoogleCalendarData;

console.log('🔧 Debug functions loaded. Use:');
console.log('- debugGoogleCalendarFlow() - Complete flow debug');
console.log('- manuallyLoadCachedEvents() - Manually load cached events');
console.log('- clearGoogleCalendarData() - Clear all data for testing');
