/**
 * Sync Status Dashboard for CalenTask
 * Provides real-time sync status and controls
 */
class SyncStatusDashboard {
  constructor(googleCalendarService) {
    this.googleCalendarService = googleCalendarService;
    this.isVisible = false;
    this.updateInterval = null;
    this.createDashboard();
    this.setupEventListeners();
  }

  /**
   * Create the dashboard UI
   */
  createDashboard() {
    // Create dashboard container
    this.dashboard = document.createElement('div');
    this.dashboard.id = 'sync-status-dashboard';
    this.dashboard.className = 'sync-dashboard hidden';
    this.dashboard.innerHTML = `
      <div class="sync-dashboard-header">
        <h3>📊 Sync Status Dashboard</h3>
        <button class="close-btn" id="close-dashboard">×</button>
      </div>
      
      <div class="sync-dashboard-content">
        <div class="status-section">
          <h4>🔄 Current Status</h4>
          <div class="status-grid">
            <div class="status-item">
              <label>Service Status:</label>
              <span id="service-status" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Authentication:</label>
              <span id="auth-status" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Sync Strategy:</label>
              <span id="sync-strategy" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Network:</label>
              <span id="network-status" class="status-value">-</span>
            </div>
          </div>
        </div>

        <div class="events-section">
          <h4>📅 Events Data</h4>
          <div class="status-grid">
            <div class="status-item">
              <label>Cached Events:</label>
              <span id="cached-events-count" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Last Sync:</label>
              <span id="last-sync-time" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Data Integrity:</label>
              <span id="data-integrity" class="status-value">-</span>
            </div>
            <div class="status-item">
              <label>Storage Usage:</label>
              <span id="storage-usage" class="status-value">-</span>
            </div>
          </div>
        </div>

        <div class="sync-controls">
          <h4>🎛️ Sync Controls</h4>
          <div class="control-buttons">
            <button id="force-sync-btn" class="control-btn primary">Force Sync</button>
            <button id="clear-cache-btn" class="control-btn secondary">Clear Cache</button>
            <button id="export-data-btn" class="control-btn secondary">Export Data</button>
            <button id="import-data-btn" class="control-btn secondary">Import Data</button>
          </div>
          
          <div class="sync-strategy-controls">
            <label for="strategy-select">Sync Strategy:</label>
            <select id="strategy-select">
              <option value="immediate">Immediate</option>
              <option value="debounced">Debounced (Default)</option>
              <option value="scheduled">Scheduled</option>
              <option value="on_demand">On Demand</option>
            </select>
          </div>
        </div>

        <div class="sync-log">
          <h4>📝 Recent Sync Activity</h4>
          <div id="sync-log-content" class="log-content">
            <p class="log-empty">No recent sync activity</p>
          </div>
        </div>
      </div>
    `;

    // Add styles
    this.addStyles();
    
    // Append to body
    document.body.appendChild(this.dashboard);
  }

  /**
   * Add CSS styles for the dashboard
   */
  addStyles() {
    if (document.getElementById('sync-dashboard-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'sync-dashboard-styles';
    styles.textContent = `
      .sync-dashboard {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 80vh;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        overflow-y: auto;
      }
      
      .sync-dashboard.hidden {
        display: none;
      }
      
      .sync-dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #ddd;
        border-radius: 8px 8px 0 0;
      }
      
      .sync-dashboard-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
      
      .close-btn {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
      }
      
      .close-btn:hover {
        background: #e9ecef;
      }
      
      .sync-dashboard-content {
        padding: 16px;
      }
      
      .status-section, .events-section, .sync-controls, .sync-log {
        margin-bottom: 20px;
      }
      
      .status-section h4, .events-section h4, .sync-controls h4, .sync-log h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        color: #495057;
      }
      
      .status-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
      }
      
      .status-item {
        display: flex;
        justify-content: space-between;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 12px;
      }
      
      .status-item label {
        font-weight: 500;
        color: #6c757d;
      }
      
      .status-value {
        font-weight: 600;
      }
      
      .status-value.online { color: #28a745; }
      .status-value.offline { color: #dc3545; }
      .status-value.warning { color: #ffc107; }
      
      .control-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-bottom: 12px;
      }
      
      .control-btn {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
        font-size: 12px;
        font-weight: 500;
      }
      
      .control-btn.primary {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      
      .control-btn:hover {
        opacity: 0.8;
      }
      
      .sync-strategy-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
      }
      
      .sync-strategy-controls select {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 12px;
      }
      
      .log-content {
        max-height: 150px;
        overflow-y: auto;
        background: #f8f9fa;
        border-radius: 4px;
        padding: 8px;
        font-size: 11px;
        font-family: monospace;
      }
      
      .log-empty {
        color: #6c757d;
        font-style: italic;
        margin: 0;
      }
      
      .log-entry {
        margin: 4px 0;
        padding: 4px;
        border-radius: 2px;
      }
      
      .log-entry.success { background: #d4edda; color: #155724; }
      .log-entry.error { background: #f8d7da; color: #721c24; }
      .log-entry.info { background: #d1ecf1; color: #0c5460; }
    `;
    
    document.head.appendChild(styles);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Close button
    this.dashboard.querySelector('#close-dashboard').addEventListener('click', () => {
      this.hide();
    });

    // Control buttons
    this.dashboard.querySelector('#force-sync-btn').addEventListener('click', () => {
      this.forceSync();
    });

    this.dashboard.querySelector('#clear-cache-btn').addEventListener('click', () => {
      this.clearCache();
    });

    this.dashboard.querySelector('#export-data-btn').addEventListener('click', () => {
      this.exportData();
    });

    this.dashboard.querySelector('#import-data-btn').addEventListener('click', () => {
      this.importData();
    });

    // Strategy selector
    this.dashboard.querySelector('#strategy-select').addEventListener('change', (e) => {
      this.changeSyncStrategy(e.target.value);
    });

    // Listen for sync events
    window.addEventListener('googleCalendarEventsUpdated', () => {
      this.updateStatus();
      this.addLogEntry('Events updated from Google Calendar', 'success');
    });
  }

  /**
   * Show the dashboard
   */
  show() {
    this.dashboard.classList.remove('hidden');
    this.isVisible = true;
    this.startAutoUpdate();
    this.updateStatus();
  }

  /**
   * Hide the dashboard
   */
  hide() {
    this.dashboard.classList.add('hidden');
    this.isVisible = false;
    this.stopAutoUpdate();
  }

  /**
   * Toggle dashboard visibility
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * Start auto-updating the status
   */
  startAutoUpdate() {
    this.stopAutoUpdate();
    this.updateInterval = setInterval(() => {
      this.updateStatus();
    }, 2000); // Update every 2 seconds
  }

  /**
   * Stop auto-updating
   */
  stopAutoUpdate() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * Update all status information
   */
  async updateStatus() {
    try {
      // Service status
      const serviceStatus = this.googleCalendarService.getSyncStatus();
      this.updateElement('service-status', serviceStatus.isReady ? 'Ready' : 'Not Ready', 
                        serviceStatus.isReady ? 'online' : 'offline');
      
      // Auth status
      this.updateElement('auth-status', serviceStatus.isAuthenticated ? 'Authenticated' : 'Not Authenticated',
                        serviceStatus.isAuthenticated ? 'online' : 'offline');
      
      // Sync strategy
      if (this.googleCalendarService.syncManager) {
        const syncStatus = this.googleCalendarService.syncManager.getSyncStatus();
        this.updateElement('sync-strategy', syncStatus.strategy);
        
        // Update strategy selector
        const strategySelect = this.dashboard.querySelector('#strategy-select');
        strategySelect.value = syncStatus.strategy;
      }
      
      // Network status
      const isOnline = navigator.onLine;
      this.updateElement('network-status', isOnline ? 'Online' : 'Offline',
                        isOnline ? 'online' : 'offline');
      
      // Events data
      this.updateElement('cached-events-count', serviceStatus.eventCount.toString());
      
      // Last sync time
      if (serviceStatus.lastSyncTime) {
        const lastSync = new Date(serviceStatus.lastSyncTime);
        const timeAgo = this.getTimeAgo(lastSync);
        this.updateElement('last-sync-time', timeAgo);
      } else {
        this.updateElement('last-sync-time', 'Never');
      }
      
      // Storage usage
      if (this.googleCalendarService.storageManager) {
        const stats = await this.googleCalendarService.storageManager.getStorageStats();
        if (stats) {
          const sizeKB = Math.round(stats.estimatedSize / 1024);
          this.updateElement('storage-usage', `${sizeKB} KB`);
          this.updateElement('data-integrity', 'Valid', 'online');
        }
      }
      
    } catch (error) {
      console.error('Error updating sync status:', error);
      this.addLogEntry(`Status update error: ${error.message}`, 'error');
    }
  }

  /**
   * Update a dashboard element
   */
  updateElement(id, text, className = '') {
    const element = this.dashboard.querySelector(`#${id}`);
    if (element) {
      element.textContent = text;
      element.className = `status-value ${className}`;
    }
  }

  /**
   * Get human-readable time ago
   */
  getTimeAgo(date) {
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  }

  /**
   * Add entry to sync log
   */
  addLogEntry(message, type = 'info') {
    const logContent = this.dashboard.querySelector('#sync-log-content');
    const timestamp = new Date().toLocaleTimeString();
    
    // Remove empty message if it exists
    const emptyMsg = logContent.querySelector('.log-empty');
    if (emptyMsg) emptyMsg.remove();
    
    // Create log entry
    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.textContent = `[${timestamp}] ${message}`;
    
    // Add to top of log
    logContent.insertBefore(entry, logContent.firstChild);
    
    // Keep only last 20 entries
    const entries = logContent.querySelectorAll('.log-entry');
    if (entries.length > 20) {
      entries[entries.length - 1].remove();
    }
  }

  /**
   * Force sync
   */
  async forceSync() {
    try {
      this.addLogEntry('Force sync initiated...', 'info');
      await this.googleCalendarService.forcSync();
      this.addLogEntry('Force sync completed successfully', 'success');
    } catch (error) {
      this.addLogEntry(`Force sync failed: ${error.message}`, 'error');
    }
  }

  /**
   * Clear cache
   */
  async clearCache() {
    try {
      if (this.googleCalendarService.storageManager) {
        await this.googleCalendarService.storageManager.clearGoogleData();
        this.addLogEntry('Cache cleared successfully', 'success');
        this.updateStatus();
      }
    } catch (error) {
      this.addLogEntry(`Clear cache failed: ${error.message}`, 'error');
    }
  }

  /**
   * Export data
   */
  async exportData() {
    try {
      if (this.googleCalendarService.storageManager) {
        const data = await this.googleCalendarService.storageManager.exportData();
        if (data) {
          const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `calentask-backup-${new Date().toISOString().split('T')[0]}.json`;
          a.click();
          URL.revokeObjectURL(url);
          this.addLogEntry('Data exported successfully', 'success');
        }
      }
    } catch (error) {
      this.addLogEntry(`Export failed: ${error.message}`, 'error');
    }
  }

  /**
   * Import data
   */
  importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      try {
        const file = e.target.files[0];
        if (file) {
          const text = await file.text();
          const data = JSON.parse(text);
          
          if (this.googleCalendarService.storageManager) {
            const success = await this.googleCalendarService.storageManager.importData(data);
            if (success) {
              this.addLogEntry('Data imported successfully', 'success');
              this.updateStatus();
              // Reload the page to reflect changes
              setTimeout(() => window.location.reload(), 1000);
            } else {
              this.addLogEntry('Import failed', 'error');
            }
          }
        }
      } catch (error) {
        this.addLogEntry(`Import failed: ${error.message}`, 'error');
      }
    };
    input.click();
  }

  /**
   * Change sync strategy
   */
  changeSyncStrategy(strategy) {
    try {
      if (this.googleCalendarService.syncManager) {
        this.googleCalendarService.syncManager.setSyncStrategy(strategy);
        this.addLogEntry(`Sync strategy changed to: ${strategy}`, 'info');
      }
    } catch (error) {
      this.addLogEntry(`Strategy change failed: ${error.message}`, 'error');
    }
  }
}

// Export to global scope
window.SyncStatusDashboard = SyncStatusDashboard;
