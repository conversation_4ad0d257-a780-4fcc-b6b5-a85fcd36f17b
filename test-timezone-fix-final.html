<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Timezone Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { border-left: 4px solid #4CAF50; }
        .error { border-left: 4px solid #f44336; }
        .warning { border-left: 4px solid #ff9800; }
        .info { border-left: 4px solid #2196F3; }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #3367D6;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .pass {
            background-color: #e8f5e8;
            border: 1px solid #4CAF50;
        }
        .fail {
            background-color: #ffebee;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>🔧 Final Google Calendar Timezone Fix Verification</h1>
    <p>This test verifies that the complete timezone fix is working correctly for Google Calendar events.</p>
    
    <div class="test-section info">
        <h2>Test Controls</h2>
        <button onclick="runFinalTimezoneTest()">🧪 Run Final Timezone Test</button>
        <button onclick="testSpecificScenarios()">📋 Test Specific Scenarios</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        // Simulate the FIXED calendar date creation logic
        function formatDateUTC(date) {
            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0');
            const day = String(date.getUTCDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        // Simulate FIXED calendar cell creation
        function createCalendarCellsFixed(weekStartLocal) {
            const cells = [];
            
            for (let dayIndex = 0; dayIndex < 7; dayIndex++) {
                // FIXED: Use UTC date creation to match formatDate()
                const dayDateUTC = new Date(Date.UTC(
                    weekStartLocal.getFullYear(),
                    weekStartLocal.getMonth(),
                    weekStartLocal.getDate() + dayIndex
                ));
                
                const dateStr = formatDateUTC(dayDateUTC);
                
                for (let hour = 0; hour < 24; hour++) {
                    for (let minutes = 0; minutes < 60; minutes += 30) {
                        const cellDate = new Date(Date.UTC(
                            weekStartLocal.getFullYear(),
                            weekStartLocal.getMonth(),
                            weekStartLocal.getDate() + dayIndex,
                            hour,
                            minutes,
                            0,
                            0
                        ));
                        
                        cells.push({
                            dateStr: dateStr,
                            dataTime: cellDate.toISOString(),
                            utcHour: cellDate.getUTCHours(),
                            utcMinutes: cellDate.getUTCMinutes(),
                            localHour: cellDate.getHours(),
                            localMinutes: cellDate.getMinutes()
                        });
                    }
                }
            }
            
            return cells;
        }

        // Simulate event processing
        function processGoogleEvent(googleApiEvent) {
            const isAllDay = !googleApiEvent.start.dateTime;
            let startDate;
            
            if (isAllDay) {
                // FIXED: Added 'Z' suffix for UTC interpretation
                startDate = new Date(googleApiEvent.start.date + 'T00:00:00Z');
            } else {
                startDate = new Date(googleApiEvent.start.dateTime);
            }
            
            // Background stores as ISO string
            const backgroundEvent = {
                date: startDate.toISOString(),
                isFullDay: isAllDay,
                isGoogleEvent: true,
                title: googleApiEvent.summary
            };
            
            // Frontend deserializes back to Date object
            const frontendEvent = {
                ...backgroundEvent,
                date: new Date(backgroundEvent.date)
            };
            
            return { backgroundEvent, frontendEvent };
        }

        // Test event-to-cell matching
        function testEventCellMatching(event, cells) {
            const eventDate = new Date(event.date);
            const dateStr = formatDateUTC(eventDate);
            const hour = eventDate.getUTCHours();
            const minutes = eventDate.getUTCMinutes();
            
            const roundedMinutes = Math.round(minutes / 30) * 30 % 60;
            const adjustedHour = (roundedMinutes === 0) ? hour : (minutes >= 45 ? hour + 1 : hour);
            
            const matchingCell = cells.find(cell => 
                cell.dateStr === dateStr &&
                cell.utcHour === adjustedHour &&
                cell.utcMinutes === roundedMinutes
            );
            
            return {
                eventInfo: {
                    dateStr,
                    utcTime: `${hour}:${minutes.toString().padStart(2, '0')}`,
                    lookingFor: `${adjustedHour}:${roundedMinutes.toString().padStart(2, '0')}`
                },
                matchingCell,
                found: !!matchingCell
            };
        }

        function runFinalTimezoneTest() {
            addResult('🧪 Running Final Timezone Test', 'Testing complete timezone fix...', 'info');
            
            const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const timezoneOffset = new Date().getTimezoneOffset();
            
            addResult('🌍 Environment Info', `
User timezone: ${userTimezone}
Timezone offset: ${timezoneOffset} minutes (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})
Current time: ${new Date().toISOString()}
            `, 'info');
            
            // Create a week of calendar cells (simulating calendar.js logic)
            const weekStart = new Date(2024, 0, 14); // January 14, 2024 (Sunday)
            const cells = createCalendarCellsFixed(weekStart);
            
            addResult('📅 Calendar Setup', `
Created ${cells.length} calendar cells for week starting ${weekStart.toDateString()}
Sample cells: ${cells.slice(0, 3).map(c => `${c.dateStr} ${c.utcHour}:${c.utcMinutes.toString().padStart(2, '0')}`).join(', ')}
            `, 'info');
            
            // Test various Google Calendar events
            const testEvents = [
                {
                    summary: '2:00 PM PST Event',
                    start: { dateTime: '2024-01-15T14:00:00-08:00' },
                    expectedLocal: '2:00 PM PST'
                },
                {
                    summary: '10:30 AM EST Event',
                    start: { dateTime: '2024-01-16T10:30:00-05:00' },
                    expectedLocal: '10:30 AM EST'
                },
                {
                    summary: '3:00 PM UTC Event',
                    start: { dateTime: '2024-01-17T15:00:00Z' },
                    expectedLocal: '3:00 PM UTC'
                },
                {
                    summary: 'All Day Event',
                    start: { date: '2024-01-18' },
                    expectedLocal: 'All day on Jan 18'
                }
            ];
            
            let passCount = 0;
            let totalTests = testEvents.length;
            
            testEvents.forEach((apiEvent, index) => {
                const { backgroundEvent, frontendEvent } = processGoogleEvent(apiEvent);
                const matchResult = testEventCellMatching(frontendEvent, cells);
                
                const testPassed = matchResult.found;
                if (testPassed) passCount++;
                
                const resultClass = testPassed ? 'pass' : 'fail';
                const resultIcon = testPassed ? '✅' : '❌';
                
                addResult(`${resultIcon} Test ${index + 1}: ${apiEvent.summary}`, `
<div class="test-result ${resultClass}">
<strong>Original API:</strong> ${apiEvent.start.dateTime || apiEvent.start.date}<br>
<strong>Expected:</strong> ${apiEvent.expectedLocal}<br>
<strong>Background stored:</strong> ${backgroundEvent.date}<br>
<strong>Frontend parsed:</strong> ${frontendEvent.date.toISOString()}<br>
<strong>Looking for cell:</strong> ${matchResult.eventInfo.dateStr} ${matchResult.eventInfo.lookingFor} UTC<br>
<strong>Cell found:</strong> ${testPassed ? 'YES' : 'NO'}<br>
${matchResult.matchingCell ? `<strong>Matched cell:</strong> ${matchResult.matchingCell.dataTime}` : '<strong>No matching cell found!</strong>'}
</div>
                `, testPassed ? 'success' : 'error');
            });
            
            const overallResult = passCount === totalTests ? 'success' : 'error';
            const overallIcon = passCount === totalTests ? '✅' : '❌';
            
            addResult(`${overallIcon} Final Test Results`, `
Tests passed: ${passCount}/${totalTests}
${passCount === totalTests ? 
    '🎉 ALL TESTS PASSED! The timezone fix is working correctly.' : 
    '⚠️ Some tests failed. The timezone fix may need additional work.'}
            `, overallResult);
        }

        function testSpecificScenarios() {
            addResult('📋 Testing Specific Scenarios', 'Testing edge cases and specific timezone scenarios...', 'info');
            
            // Test the exact scenario mentioned: "events appearing later than scheduled"
            const problematicEvent = {
                summary: 'Event appearing too late',
                start: { dateTime: '2024-01-15T14:00:00-08:00' } // 2 PM PST
            };
            
            const { frontendEvent } = processGoogleEvent(problematicEvent);
            const eventDate = new Date(frontendEvent.date);
            
            addResult('🔍 Specific Issue Analysis', `
Event: ${problematicEvent.summary}
Original time: 2:00 PM PST (${problematicEvent.start.dateTime})
Parsed UTC time: ${eventDate.toISOString()}
Local display time: ${eventDate.getHours()}:${eventDate.getMinutes().toString().padStart(2, '0')}

Expected behavior: Event should appear at 2:00 PM in user's local time
Actual behavior: Event appears at ${eventDate.getHours()}:${eventDate.getMinutes().toString().padStart(2, '0')} local time

${eventDate.getHours() === 14 ? '✅ CORRECT: Event appears at expected time' : '❌ INCORRECT: Event appears at wrong time'}
            `, eventDate.getHours() === 14 ? 'success' : 'error');
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('🚀 Page Loaded', 'Ready to test the timezone fix. Click "Run Final Timezone Test" to verify the fix.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
