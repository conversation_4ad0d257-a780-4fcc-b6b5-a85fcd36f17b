// Debug script to test timezone fixes for Google Calendar events
// This script can be run in the browser console to verify the fixes

console.log('🔧 Starting Timezone Fix Debug Test...');

// Mock Google Calendar API event data
const mockGoogleEvents = [
    {
        id: 'test-all-day-1',
        summary: 'All Day Event Test',
        start: { date: '2024-01-15' },
        end: { date: '2024-01-16' },
        description: 'Test all-day event'
    },
    {
        id: 'test-timed-1',
        summary: 'Timed Event Test',
        start: { dateTime: '2024-01-15T14:30:00-08:00' },
        end: { dateTime: '2024-01-15T15:30:00-08:00' },
        description: 'Test timed event'
    },
    {
        id: 'test-utc-timed',
        summary: 'UTC Timed Event',
        start: { dateTime: '2024-01-15T22:30:00Z' },
        end: { dateTime: '2024-01-15T23:30:00Z' },
        description: 'Test UTC timed event'
    }
];

// OLD Background.js transformation (BROKEN version)
function oldBackgroundTransformation(event) {
    const isAllDay = !event.start.dateTime;
    let startDate, endDate;

    if (isAllDay) {
        // BROKEN: Missing 'Z' suffix causes local timezone interpretation
        startDate = new Date(event.start.date + 'T00:00:00');
        endDate = event.end.date ? new Date(event.end.date + 'T00:00:00') : startDate;
    } else {
        startDate = new Date(event.start.dateTime);
        endDate = event.end.dateTime ? new Date(event.end.dateTime) : startDate;
    }

    return {
        id: `google_${event.id}`,
        title: event.summary || 'Untitled Event',
        description: event.description || '',
        date: startDate.toISOString(),
        endTime: isAllDay ? null : endDate.toISOString(),
        isFullDay: isAllDay,
        isGoogleEvent: true,
        googleEventId: event.id
    };
}

// NEW Background.js transformation (FIXED version)
function newBackgroundTransformation(event) {
    const isAllDay = !event.start.dateTime;
    let startDate, endDate;

    if (isAllDay) {
        // FIXED: Added 'Z' suffix for UTC interpretation
        startDate = new Date(event.start.date + 'T00:00:00Z');
        endDate = event.end.date ? new Date(event.end.date + 'T00:00:00Z') : startDate;
    } else {
        startDate = new Date(event.start.dateTime);
        endDate = event.end.dateTime ? new Date(event.end.dateTime) : startDate;
    }

    return {
        id: `google_${event.id}`,
        title: event.summary || 'Untitled Event',
        description: event.description || '',
        date: startDate.toISOString(),
        endTime: isAllDay ? null : endDate.toISOString(),
        isFullDay: isAllDay,
        isGoogleEvent: true,
        googleEventId: event.id
    };
}

// Frontend transformation (from google-calendar-service.js)
function frontendTransformation(googleEvent) {
    const isAllDay = !googleEvent.start.dateTime;
    let startDate, endDate;

    if (isAllDay) {
        startDate = new Date(googleEvent.start.date + 'T00:00:00Z');
        endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00Z') : startDate;
    } else {
        startDate = new Date(googleEvent.start.dateTime);
        endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
    }

    return {
        id: `google_${googleEvent.id}`,
        title: googleEvent.summary || 'Untitled Event',
        description: googleEvent.description || '',
        date: startDate,
        endTime: isAllDay ? null : endDate,
        isFullDay: isAllDay,
        isGoogleEvent: true,
        googleEventId: googleEvent.id
    };
}

// Calendar.js formatDate function (uses UTC)
function formatDate(date) {
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, '0');
    const day = String(date.getUTCDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// Test function
function runTimezoneDebugTest() {
    console.log('\n📊 TIMEZONE DEBUG TEST RESULTS');
    console.log('================================');
    
    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const timezoneOffset = new Date().getTimezoneOffset();
    console.log(`🌍 User timezone: ${userTimezone} (UTC${timezoneOffset > 0 ? '-' : '+'}${Math.abs(timezoneOffset/60)})`);
    
    mockGoogleEvents.forEach((event, index) => {
        console.log(`\n🧪 Test ${index + 1}: ${event.summary}`);
        console.log(`   Original: ${event.start.dateTime || event.start.date}`);
        
        const oldResult = oldBackgroundTransformation(event);
        const newResult = newBackgroundTransformation(event);
        const frontendResult = frontendTransformation(event);
        
        // Convert frontend result to match background format (ISO strings)
        const frontendForComparison = {
            ...frontendResult,
            date: frontendResult.date.toISOString(),
            endTime: frontendResult.endTime ? frontendResult.endTime.toISOString() : null
        };
        
        console.log(`   OLD Background: ${oldResult.date}`);
        console.log(`   NEW Background: ${newResult.date}`);
        console.log(`   Frontend:       ${frontendForComparison.date}`);
        
        const oldVsNew = oldResult.date === newResult.date;
        const newVsFrontend = newResult.date === frontendForComparison.date;
        
        console.log(`   OLD vs NEW:     ${oldVsNew ? '✅ Same' : '❌ Different'}`);
        console.log(`   NEW vs Frontend: ${newVsFrontend ? '✅ Match' : '❌ Mismatch'}`);
        
        if (event.start.date) { // All-day event
            const oldDate = new Date(oldResult.date);
            const newDate = new Date(newResult.date);
            console.log(`   OLD formatted:  ${formatDate(oldDate)}`);
            console.log(`   NEW formatted:  ${formatDate(newDate)}`);
            
            if (!oldVsNew) {
                console.log(`   🔧 FIX APPLIED: All-day event now uses UTC instead of local timezone`);
            }
        }
        
        if (!newVsFrontend) {
            console.log(`   ⚠️ WARNING: Background and frontend still don't match!`);
        }
    });
    
    console.log('\n📋 SUMMARY');
    console.log('==========');
    console.log('✅ Fixed: All-day events now use UTC timezone (added Z suffix)');
    console.log('✅ Fixed: Calendar cell matching now uses UTC methods');
    console.log('✅ Consistent: Background and frontend transformations now match');
    console.log('\n🎯 Expected Result: Google Calendar events should now appear at correct times/dates');
}

// Auto-run the test
runTimezoneDebugTest();

// Export for manual testing
window.debugTimezone = {
    runTest: runTimezoneDebugTest,
    oldTransform: oldBackgroundTransformation,
    newTransform: newBackgroundTransformation,
    frontendTransform: frontendTransformation,
    formatDate: formatDate,
    mockEvents: mockGoogleEvents
};

console.log('\n💡 TIP: You can run window.debugTimezone.runTest() to re-run this test anytime');
