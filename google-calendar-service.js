/**
 * Google Calendar Service for CalenTask Chrome Extension
 * Handles Google Calendar API interactions
 */
class GoogleCalendarService {
  constructor(authService) {
    this.authService = authService;
    this.baseUrl = 'https://www.googleapis.com/calendar/v3';
    this.calendars = [];
    this.events = [];
    this.lastSyncTime = null;
    this.syncInterval = null;
    this.isEnabled = false;
    this.isInitialized = false;

    // Initialize storage and sync managers
    this.storageManager = new LocalStorageManager();
    this.syncManager = new SyncStrategyManager(this.storageManager, authService);

    // Load settings from storage (async)
    this.initializeService();
  }

  /**
   * Initialize the service asynchronously
   */
  async initializeService() {
    try {
      await this.loadSettings();
      this.isInitialized = true;

      // If we have cached events, notify listeners immediately
      if (this.events && this.events.length > 0) {
        console.log(`🔄 Service initialized with ${this.events.length} cached events, notifying UI...`);
        this.notifyEventUpdate();
      }

      console.log('✅ GoogleCalendarService initialization completed');
    } catch (error) {
      console.error('❌ GoogleCalendarService initialization failed:', error);
      this.isInitialized = true; // Mark as initialized even if failed to prevent hanging
    }
  }

  /**
   * Load service settings from Chrome storage
   */
  async loadSettings() {
    try {
      console.log('🔄 Loading Google Calendar settings from storage...');

      // Load Google Calendar events using the new storage manager
      const { events, metadata, lastSync, isValid } = await this.storageManager.loadGoogleEvents();

      // Load other settings from Chrome storage
      const result = await chrome.storage.local.get([
        'google_calendar_enabled',
        'google_calendar_sync_interval'
      ]);

      console.log('📦 Loaded data:', {
        enabled: result.google_calendar_enabled,
        lastSync: lastSync,
        eventCount: events.length,
        isValid: isValid,
        metadata: metadata
      });

      this.isEnabled = result.google_calendar_enabled || false;
      this.lastSyncTime = lastSync;
      this.events = events;

      console.log(`✅ Loaded settings: enabled=${this.isEnabled}, events=${this.events.length}, lastSync=${this.lastSyncTime}`);

      if (this.events.length > 0) {
        console.log('📋 Sample loaded events (first 2):');
        this.events.slice(0, 2).forEach((event, index) => {
          console.log(`  Event ${index + 1}:`, {
            title: event.title,
            date: event.date,
            dateType: typeof event.date,
            isValidDate: event.date instanceof Date,
            isFullDay: event.isFullDay,
            isGoogleEvent: event.isGoogleEvent
          });
        });
      }

      const syncInterval = result.google_calendar_sync_interval || 15; // Default 15 minutes
      this.setupAutoSync(syncInterval);
    } catch (error) {
      console.error('❌ Error loading calendar settings:', error);
    }
  }

  /**
   * Save service settings to Chrome storage
   */
  async saveSettings() {
    try {
      // Save Google Calendar events using the new storage manager
      const metadata = {
        syncSource: 'google-calendar-service',
        syncTime: this.lastSyncTime
      };

      await this.storageManager.saveGoogleEvents(this.events, metadata);

      // Save other settings to Chrome storage
      await chrome.storage.local.set({
        google_calendar_enabled: this.isEnabled
      });

      console.log(`💾 Saved ${this.events.length} events using LocalStorageManager`);
    } catch (error) {
      console.error('Error saving calendar settings:', error);
    }
  }

  /**
   * Enable Google Calendar integration
   */
  async enable() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Must be authenticated to enable Google Calendar');
    }

    this.isEnabled = true;
    await this.saveSettings();
    await this.syncCalendarEvents();
    this.setupAutoSync(15); // Sync every 15 minutes
  }

  /**
   * Disable Google Calendar integration
   */
  async disable() {
    this.isEnabled = false;
    this.events = [];
    this.clearAutoSync();
    await this.saveSettings();
  }

  /**
   * Setup automatic syncing
   */
  setupAutoSync(intervalMinutes = 15) {
    this.clearAutoSync();

    if (this.isEnabled && this.authService.isAuthenticated) {
      // Use the sync manager for intelligent syncing
      this.syncManager.setSyncStrategy('debounced');

      this.syncInterval = setInterval(() => {
        this.syncManager.triggerSync('scheduled_interval').catch(error => {
          console.error('Auto-sync failed:', error);
        });
      }, intervalMinutes * 60 * 1000);

      console.log(`🔄 Auto-sync setup with ${intervalMinutes}min interval using SyncStrategyManager`);
    }
  }

  /**
   * Clear automatic syncing
   */
  clearAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  /**
   * Get list of user's calendars
   */
  async getCalendarList() {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    try {
      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/users/me/calendarList`
      );

      if (response.ok) {
        const data = await response.json();
        this.calendars = data.items || [];
        return this.calendars;
      } else {
        throw new Error(`Failed to fetch calendars: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar list:', error);
      throw error;
    }
  }

  /**
   * Get events from user's primary calendar
   */
  async getCalendarEvents(calendarId = 'primary', timeMin = null, timeMax = null) {
    if (!this.authService.isAuthenticated) {
      throw new Error('Not authenticated');
    }

    // Default to current week if no time range specified
    if (!timeMin) {
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
      weekStart.setHours(0, 0, 0, 0);
      timeMin = weekStart.toISOString();
    }

    if (!timeMax) {
      const now = new Date();
      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 6); // End of week (Saturday)
      weekEnd.setHours(23, 59, 59, 999);
      timeMax = weekEnd.toISOString();
    }

    try {
      const params = new URLSearchParams({
        timeMin: timeMin,
        timeMax: timeMax,
        singleEvents: 'true',
        orderBy: 'startTime',
        maxResults: '250'
      });

      const response = await this.authService.makeAuthenticatedRequest(
        `${this.baseUrl}/calendars/${encodeURIComponent(calendarId)}/events?${params}`
      );

      if (response.ok) {
        const data = await response.json();
        return data.items || [];
      } else {
        throw new Error(`Failed to fetch events: ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw error;
    }
  }

  /**
   * Sync calendar events from Google Calendar
   */
  async syncCalendarEvents() {
    if (!this.isEnabled || !this.authService.isAuthenticated) {
      return;
    }

    try {
      console.log('🔄 Syncing Google Calendar events...');

      // Store current events for comparison
      const previousEvents = [...this.events];
      const previousEventIds = new Set(previousEvents.map(e => e.googleEventId));

      // Get events for the current week and next week
      const now = new Date();
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - now.getDay() - 7); // Start from last week
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(now);
      weekEnd.setDate(now.getDate() - now.getDay() + 14); // End at next week
      weekEnd.setHours(23, 59, 59, 999);

      const events = await this.getCalendarEvents(
        'primary',
        weekStart.toISOString(),
        weekEnd.toISOString()
      );

      // Transform Google Calendar events to our format
      const newEvents = events.map(event => this.transformGoogleEvent(event));
      const newEventIds = new Set(newEvents.map(e => e.googleEventId));

      // Detect changes
      const addedEvents = newEvents.filter(e => !previousEventIds.has(e.googleEventId));
      const removedEventIds = [...previousEventIds].filter(id => !newEventIds.has(id));
      const updatedEvents = newEvents.filter(newEvent => {
        const previousEvent = previousEvents.find(e => e.googleEventId === newEvent.googleEventId);
        return previousEvent && (
          previousEvent.updated !== newEvent.updated ||
          previousEvent.title !== newEvent.title ||
          previousEvent.date.getTime() !== newEvent.date.getTime()
        );
      });

      // Log sync changes
      if (addedEvents.length > 0 || removedEventIds.length > 0 || updatedEvents.length > 0) {
        console.log('📊 Sync changes detected:', {
          added: addedEvents.length,
          removed: removedEventIds.length,
          updated: updatedEvents.length,
          total: newEvents.length
        });
      }

      // Update events
      this.events = newEvents;
      this.lastSyncTime = new Date().toISOString();
      await this.saveSettings();

      console.log(`✅ Synced ${this.events.length} Google Calendar events`);

      // Notify listeners about the sync
      this.notifyEventUpdate();

      return {
        events: this.events,
        changes: {
          added: addedEvents,
          removed: removedEventIds,
          updated: updatedEvents
        }
      };
    } catch (error) {
      console.error('❌ Failed to sync calendar events:', error);
      throw error;
    }
  }

  /**
   * Transform Google Calendar event to our internal format
   */
  transformGoogleEvent(googleEvent) {
    const isAllDay = !googleEvent.start.dateTime;

    let startDate, endDate;

    if (isAllDay) {
      // All-day events use date instead of dateTime. Ensure UTC context.
      // Google API provides 'YYYY-MM-DD' for all-day events. Append 'T00:00:00Z' to specify UTC.
      startDate = new Date(googleEvent.start.date + 'T00:00:00Z');
      endDate = googleEvent.end.date ? new Date(googleEvent.end.date + 'T00:00:00Z') : startDate;
    } else {
      // For timed events, Google API provides ISO 8601 strings with timezone offsets, which new Date() handles correctly.
      startDate = new Date(googleEvent.start.dateTime);
      endDate = googleEvent.end.dateTime ? new Date(googleEvent.end.dateTime) : startDate;
    }

    // Ensure created and updated are also handled as UTC if they are simple date strings (though less common for these fields)
    const createdDate = googleEvent.created ? (googleEvent.created.length === 10 ? new Date(googleEvent.created + 'T00:00:00Z') : new Date(googleEvent.created)) : null;
    const updatedDate = googleEvent.updated ? (googleEvent.updated.length === 10 ? new Date(googleEvent.updated + 'T00:00:00Z') : new Date(googleEvent.updated)) : null;

    return {
      id: `google_${googleEvent.id}`,
      title: googleEvent.summary || 'Untitled Event',
      description: googleEvent.description || '',
      date: startDate,
      endTime: isAllDay ? null : endDate,
      isFullDay: isAllDay,
      isGoogleEvent: true,
      googleEventId: googleEvent.id,
      location: googleEvent.location || '',
      attendees: googleEvent.attendees || [],
      htmlLink: googleEvent.htmlLink || '',
      status: googleEvent.status || 'confirmed',
      created: createdDate,
      updated: updatedDate
    };
  }

  /**
   * Get all synced Google Calendar events
   */
  getEvents() {
    return this.events;
  }

  /**
   * Load cached events and notify listeners (useful for ensuring events are displayed)
   */
  loadCachedEvents() {
    console.log('=== loadCachedEvents called ===');
    console.log('- Cached events count:', this.events ? this.events.length : 0);
    console.log('- Cached events array:', this.events);

    if (this.events && this.events.length > 0) {
      console.log(`✅ Loading ${this.events.length} cached Google Calendar events for display`);

      // Log sample cached events
      console.log('- Sample cached events (first 3):');
      this.events.slice(0, 3).forEach((event, index) => {
        console.log(`  Cached Event ${index + 1}:`, {
          title: event.title,
          date: event.date,
          dateType: typeof event.date,
          isValidDate: event.date instanceof Date,
          isFullDay: event.isFullDay,
          isGoogleEvent: event.isGoogleEvent
        });
      });

      console.log('🔔 Notifying listeners about cached events...');
      this.notifyEventUpdate();
      console.log('✅ Cached events notification sent');
      return this.events;
    } else {
      console.log('❌ No cached Google Calendar events available');
      return [];
    }
  }

  /**
   * Get events for a specific date range
   */
  getEventsInRange(startDate, endDate) {
    return this.events.filter(event => {
      const eventDate = new Date(event.date);
      return eventDate >= startDate && eventDate <= endDate;
    });
  }

  /**
   * Check if service is enabled and authenticated
   */
  isReady() {
    return this.isEnabled && this.authService.isAuthenticated;
  }

  /**
   * Check if service has finished initializing
   */
  isServiceInitialized() {
    return this.isInitialized;
  }

  /**
   * Wait for service to be initialized
   */
  async waitForInitialization() {
    if (this.isInitialized) {
      return true;
    }

    // Wait up to 5 seconds for initialization
    const maxWait = 5000;
    const checkInterval = 100;
    let waited = 0;

    while (!this.isInitialized && waited < maxWait) {
      await new Promise(resolve => setTimeout(resolve, checkInterval));
      waited += checkInterval;
    }

    return this.isInitialized;
  }

  /**
   * Get sync status information
   */
  getSyncStatus() {
    return {
      isEnabled: this.isEnabled,
      isAuthenticated: this.authService.isAuthenticated,
      lastSyncTime: this.lastSyncTime,
      eventCount: this.events.length,
      isReady: this.isReady()
    };
  }

  /**
   * Notify listeners about event updates
   */
  notifyEventUpdate() {
    console.log('Dispatching googleCalendarEventsUpdated event with', this.events.length, 'events');

    // Dispatch custom event for calendar updates
    const event = new CustomEvent('googleCalendarEventsUpdated', {
      detail: {
        events: this.events,
        syncTime: this.lastSyncTime
      }
    });

    window.dispatchEvent(event);
    console.log('googleCalendarEventsUpdated event dispatched');
  }

  /**
   * Force a manual sync
   */
  async forcSync() {
    if (!this.isReady()) {
      throw new Error('Google Calendar service not ready');
    }

    return await this.syncCalendarEvents();
  }

  /**
   * Perform automatic sync on page load/refresh
   * This method includes additional checks and error handling for automatic sync
   */
  async autoSyncOnPageLoad() {
    try {
      // Check if we should perform auto-sync
      const syncCheck = this.shouldAutoSync();

      if (!syncCheck.shouldSync) {
        console.log(`Auto-sync skipped: ${syncCheck.message}`);

        // Even if we skip the sync, we should still load cached events
        if (this.events && this.events.length > 0) {
          console.log(`Loading ${this.events.length} cached Google Calendar events (sync skipped due to ${syncCheck.reason})`);

          // Notify listeners about the cached events to ensure UI updates
          this.notifyEventUpdate();

          // Return the cached events with detailed information
          return {
            syncPerformed: false,
            eventsLoaded: true,
            events: this.events,
            source: 'cache',
            skipReason: syncCheck.reason,
            skipMessage: syncCheck.message,
            lastSyncTime: this.lastSyncTime
          };
        } else {
          console.log(`No cached events available (sync skipped due to ${syncCheck.reason})`);
          return {
            syncPerformed: false,
            eventsLoaded: false,
            events: [],
            source: 'none',
            skipReason: syncCheck.reason,
            skipMessage: syncCheck.message
          };
        }
      }

      console.log(`Performing automatic Google Calendar sync on page load (${syncCheck.message})...`);

      // Perform the sync
      const events = await this.syncCalendarEvents();

      console.log(`Auto-sync completed: ${events ? events.length : 0} events synced from API`);
      return {
        syncPerformed: true,
        eventsLoaded: true,
        events: events || [],
        source: 'api',
        syncTime: this.lastSyncTime
      };
    } catch (error) {
      console.error('Auto-sync on page load failed:', error);

      // Even if sync fails, try to load cached events
      if (this.events && this.events.length > 0) {
        console.log(`Loading ${this.events.length} cached events after sync failure`);
        this.notifyEventUpdate();
        return {
          syncPerformed: false,
          eventsLoaded: true,
          events: this.events,
          source: 'cache_fallback',
          error: error.message
        };
      }

      // Don't throw the error for auto-sync, just log it
      // This prevents the page load from being interrupted
      return {
        syncPerformed: false,
        eventsLoaded: false,
        events: [],
        source: 'error',
        error: error.message
      };
    }
  }

  /**
   * Check if automatic sync should be performed
   * Returns an object with sync decision and reason
   */
  shouldAutoSync() {
    // Must be enabled and authenticated
    if (!this.isReady()) {
      return {
        shouldSync: false,
        reason: 'service_not_ready',
        message: 'Google Calendar service not ready (not enabled or not authenticated)'
      };
    }

    // Check if we've synced recently (within last 5 minutes)
    // to avoid excessive syncing on rapid page refreshes
    if (this.lastSyncTime) {
      const lastSync = new Date(this.lastSyncTime);
      const now = new Date();
      const timeDiff = now.getTime() - lastSync.getTime();
      const fiveMinutesInMs = 5 * 60 * 1000;

      if (timeDiff < fiveMinutesInMs) {
        const minutesAgo = Math.round(timeDiff / (60 * 1000));
        return {
          shouldSync: false,
          reason: 'recent_sync',
          message: `Recent sync detected (${minutesAgo} minutes ago, cooldown: 5 minutes)`,
          lastSyncTime: this.lastSyncTime,
          timeSinceLastSync: timeDiff
        };
      }
    }

    return {
      shouldSync: true,
      reason: 'ready',
      message: 'Ready to sync'
    };
  }

  /**
   * Serialize events for storage (convert Date objects to ISO strings)
   */
  serializeEvents(events) {
    return events.map(event => ({
      ...event,
      date: event.date instanceof Date ? event.date.toISOString() : event.date,
      endTime: event.endTime instanceof Date ? event.endTime.toISOString() : event.endTime,
      created: event.created instanceof Date ? event.created.toISOString() : event.created,
      updated: event.updated instanceof Date ? event.updated.toISOString() : event.updated
    }));
  }

  /**
   * Deserialize events from storage (convert ISO strings back to Date objects)
   */
  deserializeEvents(events) {
    if (!Array.isArray(events)) {
      console.warn('deserializeEvents received non-array input:', events);
      return [];
    }
    return events.map(event => {
      if (typeof event !== 'object' || event === null) {
        console.warn('deserializeEvents found invalid event object:', event);
        return event; // or return null / skip
      }

      // Helper function to safely parse date strings
      const parseDateString = (dateStr, fieldName, eventTitle) => {
        if (typeof dateStr === 'string' && dateStr.trim() !== '') {
          const d = new Date(dateStr);
          if (isNaN(d.getTime())) {
            console.warn(`Failed to parse ${fieldName} for event '${eventTitle || 'Unknown'}': Invalid date string '${dateStr}'`);
            return null; // Or keep original invalid string, or a default valid date
          }
          return d;
        }
        if (dateStr instanceof Date) return dateStr; // Already a Date object
        return dateStr; // Return as-is if not a string or already a Date (e.g. null, undefined)
      };

      const deserializedEvent = {
        ...event,
        date: parseDateString(event.date, 'date', event.title),
        endTime: parseDateString(event.endTime, 'endTime', event.title),
        created: parseDateString(event.created, 'created', event.title),
        updated: parseDateString(event.updated, 'updated', event.title)
      };

      // Optional: Add a log to verify the kind of date after deserialization
      // if (deserializedEvent.date instanceof Date && !isNaN(deserializedEvent.date.getTime())) {
      //   console.log(`Deserialized event '${deserializedEvent.title}', date: ${deserializedEvent.date.toISOString()}, kind: (UTC)`);
      // } else if (deserializedEvent.date) {
      //   console.warn(`Deserialized event '${deserializedEvent.title}' has invalid date:`, deserializedEvent.date);
      // }
      return deserializedEvent;
    });
  }
}

// Export for use in other modules
window.GoogleCalendarService = GoogleCalendarService;
