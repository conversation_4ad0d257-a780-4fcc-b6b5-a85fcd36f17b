* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
    height: 100vh;
    margin: 0;
    overflow: hidden;
}

/* Dashboard Layout */
.dashboard {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Task Panel (Left) */
.task-panel {
    width: 35%;
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
    background-color: #f5f5f5;
    box-sizing: border-box;
}

/* Calendar Panel (Right) */
.calendar-panel {
    width: 65%;
    height: 100%;
    padding: 1rem;
    overflow-y: auto;
    background-color: white;
    box-sizing: border-box;
}

header {
    margin-bottom: 1rem;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.todo-header {
    margin-top: 0.4rem;
    margin-bottom: 0.4rem;
    text-align: left;
    margin-right: auto;
}

.calendar-header {
    margin-bottom: 1rem;
}


.calendar-header h2 {
    margin: 0;
    color: #4a4a4a;
}

header h1 {
    font-size: 1.5rem;
    color: #4a4a4a;
    /* margin-bottom: 0.7rem; */
}

#date-display {
    font-size: 1.1rem;
    color: #777;
}

.task-input {
    background: white;
    border-radius: 8px;
    padding: 0.7rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.7rem;
}

.task-input input,
.task-input textarea,
.modal-content input,
.modal-content textarea {
    width: 100%;
    padding: 0.4rem 0.6rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.task-input textarea,
.modal-content textarea {
    min-height: 40px;
    resize: vertical;
}

button {
    background-color: #4e7eff;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #3b5fd9;
}

.form-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.shortcut-hint {
    font-size: 0.75rem;
    color: #777;
    display: flex;
    align-items: center;
    gap: 5px;
}

.task-list-container, .archive-container {
    background: white;
    border-radius: 8px;
    padding: 0.7rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.7rem;
}

.task-list-container h2, .archive-container h2 {
    margin-bottom: 0.5rem;
    color: #4a4a4a;
    font-size: 1.1rem;
}

.task-list {
    list-style-type: none;
}

.task-item {
    background: #f9f9f9;
    padding: 0.7rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    cursor: move;
    transition: transform 0.2s, box-shadow 0.2s;
}

.task-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.task-item.dragging {
    opacity: 0.5;
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Draggable task styles */
.draggable-task {
    cursor: grab;
}

.draggable-task:active {
    cursor: grabbing;
}

/* Completed task styles */
.task-item.completed .task-title {
    text-decoration: line-through;
    color: #888;
}

.task-item.completed .task-description {
    color: #999;
}

.task-item.completed {
    background-color: #f5f5f5;
    border-left: 3px solid #4caf50;
}

.task-content {
    flex: 1;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.task-text {
    flex: 1;
}

.task-title {
    font-weight: 600;
    margin-bottom: 0.3rem;
    transition: text-decoration 0.2s, color 0.2s;
}

.task-description {
    color: #666;
    font-size: 0.9rem;
}

.task-schedule {
    color: #4e7eff;
    font-size: 0.8rem;
    margin-top: 0.3rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.task-actions {
    display: flex;
    gap: 0.8rem;
}

.task-actions button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.3rem;
    color: #777;
    transition: color 0.2s;
}

.edit-btn:hover {
    color: #4e7eff;
}

.delete-btn:hover {
    color: #ff4e4e;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 1.2rem;
    border-radius: 8px;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.modal-content .form-actions {
    margin-top: 0.5rem;
}

.close-modal {
    float: right;
    font-size: 1.5rem;
    cursor: pointer;
    color: #777;
}

.close-modal:hover {
    color: #333;
}

/* Settings styles */
#settings-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 1.2rem;
    padding: 0.3rem;
    cursor: pointer;
    transition: color 0.2s;
    margin-left: 10px;
}

#settings-btn:hover {
    color: #4e7eff;
}

.icon-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    transition: color 0.2s;
}

.icon-btn:hover {
    color: #4e7eff;
}

/* Settings modal tabs */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 1rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    color: #666;
    font-size: 0.9rem;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #4e7eff;
    border-bottom: 2px solid #4e7eff;
}

.tab-content {
    padding: 1rem 0;
    display: none;
}

.tab-content.active {
    display: block;
}

/* Blocklist styles */
.blocking-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

/* Notification settings styles */
.notification-settings {
    padding: 1rem 0;
}

.notification-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.notification-permission-status {
    margin-bottom: 1rem;
    padding: 0.8rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #eee;
}

.permission-granted {
    color: #28a745;
    font-weight: 500;
}

.permission-denied {
    color: #dc3545;
    font-weight: 500;
}

.permission-default {
    color: #ffc107;
    font-weight: 500;
}

.notification-info {
    margin-top: 1rem;
    padding: 0.8rem;
    background-color: #e3efff;
    border-radius: 4px;
    color: #4e7eff;
}

.hidden {
    display: none;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-right: 10px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4e7eff;
}

input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4e7eff;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 0.9rem;
    color: #333;
}

.blocking-info {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.85rem;
    color: #666;
}

.blocking-info p {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-info {
    font-weight: 600;
}

.blocklist-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.blocklist-input input {
    flex: 1;
}

.validation-message {
    color: #ff4e4e;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    min-height: 1rem;
}

.blocklist-container {
    margin-top: 1rem;
}

.blocklist {
    list-style-type: none;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.blocklist li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.blocklist li:last-child {
    border-bottom: none;
}

.blocklist .delete-url-btn {
    color: #ff4e4e;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.2rem;
    font-size: 0.9rem;
}

.blocklist .delete-url-btn:hover {
    color: #ff2525;
}

/* Notification styles */
.notification {
    position: fixed;
    bottom: -100px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    padding: 1rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 90%;
    width: 500px;
    transition: bottom 0.3s ease-in-out;
}

.notification.show {
    bottom: 20px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-content i {
    color: #4e7eff;
    font-size: 1.2rem;
}

.close-notification {
    background: none;
    border: none;
    color: #777;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.3rem;
}

.close-notification:hover {
    color: #333;
}

/* Archive styles */
.archive-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.archive-list {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.archive-list.expanded {
    max-height: 1000px;
}

.toggle-btn {
    background: none;
    border: none;
    color: #777;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.3s, color 0.2s;
    padding: 0.5rem;
}

.toggle-btn:hover {
    color: #4e7eff;
}

.toggle-btn i {
    transition: transform 0.3s;
}

.toggle-btn.active i {
    transform: rotate(180deg);
}

.archive-item {
    opacity: 0.7;
}

/* Custom Calendar Styles */

/* Full day row styles */
.full-day-row {
    display: grid;
    grid-template-columns: 70px repeat(7, 1fr); /* Match time-row grid columns */
    border-bottom: 1px solid #e0e0e0;
    min-height: 32px; /* Increased height to accommodate multiple events */
    background-color: #f8f9fa;
    width: 100%;
}

.full-day-corner {
    grid-column: 1;
    padding: 8px;
    font-size: 0.8rem;
    font-weight: 500;
    color: #666;
    background-color: #f1f3f5;
    border-right: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: sticky;
    left: 0;
    z-index: 2;
}

.full-day-cell {
    min-height: 22px; /* Even more compact height */
    border-right: 1px solid #e0e0e0;
    position: relative;
    padding: 0; /* Remove padding to prevent expansion */
    background-color: #f8f9fa;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    z-index: 1;
    min-width: 0; /* Allow flex items to shrink below content size */
}

.full-day-cell.drag-over {
    background-color: #e9ecef;
    border: 2px dashed #4e7eff;
}

/* Container for full-day events */
.full-day-events-container {
    max-height: 60px;
    overflow-y: auto;
    padding: 1px;
    display: flex;
    flex-direction: column;
    gap: 0px;
}

.full-day-events-container::-webkit-scrollbar {
    width: 3px;
}

.full-day-events-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.full-day-events-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.full-day-event {
    background-color: #007bff;
    border-radius: 2px;
    padding: 0px 3px;
    margin: 1px 1px 0px 1px;
    font-size: 0.7rem;
    cursor: move;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    display: flex;
    align-items: center;
    transition: background-color 0.2s, box-shadow 0.2s;
    color: white;
    width: calc(100% - 2px);
    box-sizing: border-box;
    height: 18px;
}

.full-day-event.archived {
    background-color: #28a745;
}

.full-day-event:hover {
opacity: 0.9;
}

.full-day-event.dragging {
opacity: 0.6;
border: 2px dashed #fff;
box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
transform: scale(1.05);
}

.full-day-event .event-title {
font-weight: normal;
flex: 1;
overflow: hidden;
text-overflow: ellipsis;
}

.full-day-event .event-time {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.9);
    white-space: nowrap;
    margin-right: 4px;
}

.full-day-event .delete-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0;
    opacity: 0;
    transition: opacity 0.2s, color 0.2s;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.full-day-event:hover .delete-btn {
    opacity: 1;
}

.full-day-event .delete-btn:hover {
    color: #fff;
}

#calendar {
    height: calc(100% - 50px);
    background-color: white;
    min-height: 500px;
    width: 100%;
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #ddd;
}

.calendar-nav {
    display: flex;
    gap: 10px;
}

.calendar-nav button {
    background-color: #4e7eff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.calendar-title {
    font-weight: 600;
    font-size: 1.1rem;
}

.calendar-body {
    flex-grow: 1;
    overflow-y: auto;
    position: relative;
    font-size: 0.8rem;
}

/* Time Grid Styles */

.day-header-row {
    display: grid;
    grid-template-columns: 70px repeat(7, 1fr);
    position: sticky;
    top: 0;
    z-index: 501;
    background-color: white;
}

.day-header {
    padding: 4px 0;
    text-align: center;
    font-weight: 600;
    background-color: #f5f5f5;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    font-size: 0.8rem;
}

.day-header.today {
    background-color: #e3efff;
    color: #4e7eff;
}

.day-date {
    font-size: 0.7rem;
    font-weight: 400;
    color: #666;
}

.time-row {
    display: grid;
    grid-template-columns: 70px repeat(7, 1fr);
    min-height: 24px;
}

.full-hour-row {
    border-top: 1px solid #ddd;
    box-sizing: border-box;
}

.time-label {
    padding: 3px 5px;
    text-align: right;
    font-size: 0.7rem;
    color: #666;
    border-right: 1px solid #ddd;
    /* border-bottom: 1px solid #ddd; */
    background-color: #f9f9f9;
    height: 100%;
    box-sizing: border-box;
}

.time-label.hour-span {
    grid-row: span 2;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    /* border-bottom: 1px solid #ddd; */
    font-size: 0.7rem;
}

.time-label-placeholder {
    width: 70px;
    grid-column: 1;
    background-color: #f9f9f9;
    border-right: 1px solid #ddd;
}

.time-cell {
    border-right: 1px solid #ddd;
    /* border-bottom: 1px solid #ddd; */
    padding: 1px;
    position: relative;
    min-height: 24px;
    box-sizing: border-box;
}

.time-cell.drag-over {
    background-color: rgba(78, 126, 255, 0.2);
    box-shadow: inset 0 0 0 2px #4e7eff;
}

/* Task list drop target styles */
.task-list-container.drag-target {
    background-color: rgba(78, 126, 255, 0.1);
    box-shadow: 0 0 0 2px #4e7eff;
}

.task-item.calendar-drop-target {
    background-color: rgba(78, 126, 255, 0.15);
    box-shadow: 0 0 0 2px #4e7eff;
    transform: scale(1.02);
    transition: all 0.1s ease;
}

.full-hour-row .time-cell {
    /* border-bottom: 1px solid #ddd; */
    background-color: #fcfcfc;
    box-sizing: border-box;
}

.time-cell:nth-child(8n) {
    border-right: 1px solid #ddd;
}

.time-cell.current-time {
    background-color: rgba(78, 126, 255, 0.1);
}

.day-column.today .time-cell {
    background-color: #f8fbff;
}

.time-event {
    position: absolute;
    cursor: pointer;
    user-select: none;
    box-sizing: border-box;
    z-index: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 2px);
    display: flex;
    flex-direction: column;
    border: none;
    margin: 1px;
    padding: 1px 3px;
    /* Ensure border is included in height calculations */
    box-sizing: border-box;
}

.time-event .resize-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 8px;
    cursor: ns-resize;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s;
    background: transparent;
}

.time-event .resize-handle::after {
    content: '';
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
}

.time-event:hover .resize-handle,
.time-event.resizing .resize-handle {
    opacity: 1;
}

.time-event.resizing {
    opacity: 0.9;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 500;
    pointer-events: none;
}

.time-event.resizing .resize-handle {
    pointer-events: auto;
}

.time-event {
    border-radius: 2px;
    font-size: 0.7rem;
    background-color: #007bff;
    color: white;
    cursor: pointer;
}

.time-event.archived {
    background-color: #28a745; /* Green for archived tasks */
}

.time-event.dragging-event {
    opacity: 0.6;
    border: 2px dashed #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
}

.time-event:hover {
    opacity: 0.9;
}

/* Responsive styles */
@media (max-width: 992px) {
    .dashboard {
        flex-direction: column;
    }

    .task-panel,
    .calendar-panel {
        width: 100%;
        height: auto;
    }

    .task-panel {
        max-height: 50vh;
    }

    .calendar-panel {
        height: 50vh;
    }

    #calendar {
        height: calc(100% - 50px);
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .task-input, .task-list-container {
        padding: 1rem;
    }

    .modal-content {
        width: 90%;
    }
}

/* Custom checkbox styles */
.task-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-top: 3px;
    cursor: pointer;
}

.task-checkbox input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border: 2px solid #ddd;
    border-radius: 4px;
    transition: all 0.2s;
}

.task-checkbox:hover input ~ .checkmark {
    border-color: #ccc;
}

.task-checkbox input:checked ~ .checkmark {
    background-color: #1b8608;
    border-color: #1b8608;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.task-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.task-checkbox .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Google Calendar Integration Styles */

/* Google Calendar Settings */
.google-calendar-settings {
    padding: 1rem 0;
}

.google-auth-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.auth-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
    padding: 0.8rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #eee;
}

.auth-status.connected {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.auth-status .status-text {
    font-weight: 500;
}

.auth-status .user-info {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

.auth-buttons {
    display: flex;
    gap: 10px;
}

.primary-btn {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.primary-btn:hover {
    background-color: #3367d6;
}

.primary-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.secondary-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.secondary-btn:hover {
    background-color: #5a6268;
}

.secondary-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.google-calendar-toggle {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.google-calendar-sync {
    margin-bottom: 1.5rem;
}

.sync-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.sync-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.sync-buttons button {
    flex: 1;
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
}

.sync-info {
    font-size: 0.9rem;
    color: #666;
}

.google-calendar-info {
    margin-top: 1rem;
    padding: 0.8rem;
    background-color: #e3f2fd;
    border-radius: 4px;
    color: #1565c0;
    font-size: 0.85rem;
}

.google-calendar-info p {
    margin-bottom: 0.5rem;
}

.google-calendar-info p:last-child {
    margin-bottom: 0;
}

/* Google Calendar Events */
.google-full-day-event {
    background-color: #4285f4 !important;
    border-radius: 2px;
    padding: 0px 3px;
    margin: 1px 1px 0px 1px;
    font-size: 0.7rem;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s, box-shadow 0.2s;
    color: white;
    width: calc(100% - 2px);
    box-sizing: border-box;
    height: 18px;
    opacity: 0.9;
    border-left: 3px solid #1a73e8;
}

.google-full-day-event:hover {
    opacity: 1;
    background-color: #3367d6 !important;
}

.google-full-day-event .event-title {
    font-weight: normal;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.google-full-day-event .event-time {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.9);
    white-space: nowrap;
    margin-right: 4px;
}

.google-full-day-event .google-event-indicator {
    font-size: 0.6rem;
    margin-left: 4px;
    opacity: 0.8;
}

.google-time-event {
    position: absolute;
    cursor: pointer;
    user-select: none;
    box-sizing: border-box;
    z-index: 4; /* Lower than CalenTask events */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 2px);
    display: flex;
    flex-direction: column;
    border: none;
    margin: 1px;
    padding: 1px 3px;
    border-radius: 2px;
    font-size: 0.7rem;
    background-color: #4285f4 !important;
    color: white;
    opacity: 0.85;
    border-left: 3px solid #1a73e8;
    box-sizing: border-box;
}

.google-time-event:hover {
    opacity: 1;
    background-color: #3367d6 !important;
}

/* Notification styles for different types */
.notification.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.notification.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.notification.info {
    background-color: #d1ecf1;
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

.notification.success .notification-content i {
    color: #28a745;
}

.notification.error .notification-content i {
    color: #dc3545;
}

.notification.info .notification-content i {
    color: #17a2b8;
}
