# Google Calendar Integration Setup

This guide explains how to set up Google Calendar integration for the CalenTask Chrome extension.

## Prerequisites

1. A Google account with Google Calendar access
2. Chrome browser with developer mode enabled
3. CalenTask extension installed

## Google Cloud Console Setup

### Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" and then "New Project"
3. Enter a project name (e.g., "CalenTask Extension")
4. Click "Create"

### Step 2: Enable Google Calendar API

1. In your project, go to "APIs & Services" > "Library"
2. Search for "Google Calendar API"
3. Click on "Google Calendar API" and then "Enable"

### Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in the required fields:
     - App name: "CalenTask"
     - User support email: Your email
     - Developer contact information: Your email
   - Add scopes: `https://www.googleapis.com/auth/calendar.readonly`
   - Add test users (your email) if needed
4. For the OAuth client ID:
   - Application type: "Chrome extension"
   - Name: "CalenTask Extension"
   - Application ID: Your Chrome extension ID (found in chrome://extensions/)
5. Click "Create"
6. Copy the Client ID

### Step 4: Update Extension Manifest

1. Open the `manifest.json` file in your extension
2. Replace `YOUR_GOOGLE_CLIENT_ID.apps.googleusercontent.com` with your actual Client ID:

```json
"oauth2": {
  "client_id": "your-actual-client-id.apps.googleusercontent.com",
  "scopes": ["https://www.googleapis.com/auth/calendar.readonly"]
}
```

## Extension Installation

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the extension folder
4. Note the Extension ID that appears
5. If you haven't already, update the OAuth client ID in Google Cloud Console with this Extension ID

## Using Google Calendar Integration

### Initial Setup

1. Open the CalenTask extension
2. Click the settings gear icon
3. Go to the "Google Calendar" tab
4. Click "Sign in with Google"
5. Grant the necessary permissions
6. Toggle "Show Google Calendar events in weekly view" to enable

### Features

- **Read-only Access**: Google Calendar events are displayed but cannot be edited
- **Visual Distinction**: Google Calendar events appear in blue with a calendar icon (📅)
- **Automatic Sync**: Events sync every 15 minutes automatically
- **Manual Sync**: Use the "Sync now" button for immediate updates
- **Event Details**: Click on Google Calendar events to view details

### Event Display

- **Full-day Events**: Appear in the "All Day" row with blue background
- **Timed Events**: Appear in time slots with blue background and calendar icon
- **Layering**: Google Calendar events appear behind CalenTask events (lower z-index)
- **Non-interactive**: Cannot be dragged, resized, or edited

## Troubleshooting

### Authentication Issues

1. **"Authentication failed"**: 
   - Verify the Client ID in manifest.json matches Google Cloud Console
   - Check that the extension ID in Google Cloud Console is correct
   - Ensure Google Calendar API is enabled

2. **"Permission denied"**:
   - Make sure you've granted calendar read permissions
   - Check OAuth consent screen configuration
   - Verify the scope `https://www.googleapis.com/auth/calendar.readonly` is included

### Sync Issues

1. **Events not appearing**:
   - Check if Google Calendar integration is enabled in settings
   - Try manual sync using "Sync now" button
   - Verify you have events in your Google Calendar for the current week

2. **Outdated events**:
   - Events sync automatically every 15 minutes
   - Use manual sync for immediate updates
   - Check browser console for sync errors

### Performance

- The extension only syncs events for a 3-week window (last week, current week, next week)
- Maximum 250 events are synced per request
- Events are cached locally for offline viewing

## Privacy and Security

- **Local Storage**: Google Calendar events are stored locally in Chrome storage
- **No External Servers**: Data is never sent to external servers (except Google's APIs)
- **Read-only Access**: The extension cannot modify your Google Calendar
- **Minimal Permissions**: Only requests calendar read access, no other Google services

## API Limits

Google Calendar API has the following limits:
- 1,000,000 queries per day
- 100 queries per 100 seconds per user
- The extension is designed to stay well within these limits with periodic syncing

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Google Cloud Console setup
3. Ensure all permissions are granted
4. Try signing out and back in to Google Calendar integration

## Development Notes

For developers working on this integration:
- Authentication uses Chrome Identity API
- Events are transformed to match CalenTask's internal format
- Background sync runs every 15 minutes when enabled
- UI updates are triggered via custom events
- Error handling includes token refresh and API rate limiting
