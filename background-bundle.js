// Combined background script for CalenTask

// ===== CONSTANTS =====
const USER_BLOCKED_SITES_KEY = 'userBlockedSites'; // Stores array of match patterns
const TASKS_KEY = 'tasks';
const CONTENT_SCRIPT_ID_PREFIX = 'calentask-blocker-';

// ===== GLOBAL STATE (populated from storage) =====
let blockedSitePatterns = []; // Will hold patterns like "*://*.example.com/*"
let tasks = []; // Already used, will be loaded consistently
let isBlockingEnabled = true; // Existing global
// blockingStatus also exists

// ===== INITIALIZATION AND STORAGE SYNC =====

// Function to load tasks from storage
async function loadTasksFromStorage() {
  try {
    const result = await chrome.storage.local.get([TASKS_KEY]);
    tasks = result.tasks || [];
    console.log('Tasks loaded/updated for service worker:', tasks.length);
  } catch (error) {
    console.error('Error loading tasks from storage:', error);
    tasks = [];
  }
}

// Function to load blocked site patterns from storage
async function loadBlockedSitePatternsFromStorage() {
  try {
    const result = await chrome.storage.local.get([USER_BLOCKED_SITES_KEY]);
    blockedSitePatterns = result[USER_BLOCKED_SITES_KEY] || [];
    console.log('Blocked site patterns loaded/updated for service worker:', blockedSitePatterns.length);
  } catch (error) {
    console.error('Error loading blocked site patterns from storage:', error);
    blockedSitePatterns = [];
  }
}

// Function to update dynamic content script registrations
async function updateDynamicContentScripts() {
  console.log('Updating dynamic content scripts...');
  await loadBlockedSitePatternsFromStorage(); // Ensure we have the latest patterns

  try {
    const oldScripts = await chrome.scripting.getRegisteredContentScripts();
    const oldScriptIds = oldScripts
      .filter(script => script.id.startsWith(CONTENT_SCRIPT_ID_PREFIX))
      .map(script => script.id);

    if (oldScriptIds.length > 0) {
      console.log('Unregistering old CalenTask content scripts:', oldScriptIds);
      await chrome.scripting.unregisterContentScripts({ ids: oldScriptIds });
    }

    if (blockedSitePatterns && blockedSitePatterns.length > 0) {
      const newScripts = blockedSitePatterns.map((pattern, index) => ({
        id: `${CONTENT_SCRIPT_ID_PREFIX}${index}`,
        matches: [pattern],
        js: ['content-blocker.js'],
        runAt: 'document_start',
        persistAcrossSessions: true,
      }));
      console.log('Registering new CalenTask content scripts:', newScripts.length);
      await chrome.scripting.registerContentScripts(newScripts);
    } else {
      console.log('No blocked site patterns to register. All CalenTask content scripts (if any) were unregistered.');
    }
    console.log('Dynamic content scripts update process complete.');
  } catch (error) {
    console.error('Error updating dynamic content scripts:', error);
  }
}

// ===== SERVICE WORKER LIFECYCLE AND EVENT LISTENERS =====

chrome.runtime.onInstalled.addListener(async (details) => {
  console.log('CalenTask installed or updated:', details.reason);
  await loadTasksFromStorage();
  await updateDynamicContentScripts();
  updateBadgeCount(); // Initial badge update
});

// Initial load when service worker starts/wakes up
(async () => {
  console.log('CalenTask service worker starting/re-activating...');
  await loadTasksFromStorage();
  await loadBlockedSitePatternsFromStorage(); // Load initial patterns for any immediate checks
  updateBadgeCount();
  await updateDynamicContentScripts(); // Ensure scripts are correctly registered
})();

// ===== BACKGROUND.JS (Original functionality starts here) =====
// Function to update the badge with the count of active tasks
function updateBadgeCount() {
  chrome.storage.local.get(['tasks'], (result) => {
    const tasks = result.tasks || [];
    const activeTaskCount = tasks.length;
    
    // Set the badge text to the number of active tasks
    if (activeTaskCount > 0) {
      chrome.action.setBadgeText({ text: activeTaskCount.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#ff0000' }); // Red background
      
      // Note: Badge text color is automatically set to white for contrast in most Chrome versions
      // The setBadgeTextColor API might not be available in all Chrome versions
      if (chrome.action.setBadgeTextColor) {
        chrome.action.setBadgeTextColor({ color: '#ffffff' }); // White text
      }
    } else {
      chrome.action.setBadgeText({ text: '' }); // Clear the badge if no tasks
    }
  });
}

// Listen for clicks on the extension icon
chrome.action.onClicked.addListener((tab) => {
  // Open the task manager in a new tab
  chrome.tabs.create({ url: 'todo.html' });
});

// Listen for changes to the storage
chrome.storage.onChanged.addListener(async (changes, namespace) => {
  if (namespace === 'local') {
    let needsScriptUpdate = false;
    let needsTaskRelatedUpdates = false;

    if (changes[TASKS_KEY]) {
      console.log('Tasks changed in storage (background listener).');
      await loadTasksFromStorage(); // Update global tasks variable
      needsTaskRelatedUpdates = true;
    }
    if (changes[USER_BLOCKED_SITES_KEY]) {
      console.log('Blocked sites list changed in storage (background listener).');
      // loadBlockedSitePatternsFromStorage() is called inside updateDynamicContentScripts()
      needsScriptUpdate = true;
    }
    // Example: if you store isBlockingEnabled and want to react
    // if (changes.isBlockingEnabledStorageKey) { 
    //   await loadBlockingEnabledStatusFromStorage(); 
    //   // Potentially re-evaluate blocking or update scripts if they depend on this flag directly
    // }

    if (needsScriptUpdate) {
      await updateDynamicContentScripts();
    }
    if (needsTaskRelatedUpdates) {
      updateBadgeCount(); // Update badge
      // If content-blocker.js relies on active task status, it will fetch the latest tasks itself.
    }
  }
});

// Update badge when the extension is loaded (now handled by IIFE and onInstalled)

// ===== BLOCKER.JS (Logic, now uses global 'tasks' and 'blockedSitePatterns') =====
// Website blocker functionality for CalenTask
// 'blockedSitePatterns' and 'tasks' are now global and loaded from storage.
// 'isBlockingEnabled' is now a global variable defined at the top.
let blockingStatus = {
  isBlocking: false,
  currentTask: null,
  blockedAttempts: 0
};

// Function to check if a URL should be blocked
function shouldBlockUrl(url) {
  console.log('Checking if URL should be blocked:', url);
  
  if (!isBlockingEnabled) {
    console.log('Blocking is disabled');
    return false;
  }
  
  if (blockedSitePatterns.length === 0) {
    console.log('No blocked URLs configured');
    return false;
  }

  // Extract domain from URL
  const domain = extractDomain(url);
  console.log('Extracted domain:', domain);
  console.log('Blocked site patterns list:', blockedSitePatterns);
  
  // Check if the domain is in the blocklist
  // NOTE: The following matching logic in shouldBlockUrl is basic and might not correctly handle complex Chrome match patterns.
  // It assumes blockedSitePatterns contains simple domain strings or URLs where extractDomain is effective.
  // For robust pattern matching, content-blocker.js should ideally use URLPattern API or the service worker should perform the match if patterns are complex.
  const shouldBlock = blockedSitePatterns.some(pattern => {
    // This is a simplified matching logic. If 'pattern' is a full Chrome match pattern (e.g., '*://*.example.com/*'),
    // 'extractDomain(pattern)' will not work correctly. A more robust solution would involve a proper URL matching library or API.
    // For now, it assumes patterns are simple enough that extractDomain gives a meaningful part to check against the current page's domain.
    const patternDomain = extractDomain(pattern);
    const currentDomain = extractDomain(url); // 'domain' variable was already extracted, using it directly
    const isMatch = currentDomain.includes(patternDomain);
    console.log(`Checking pattern: ${pattern} (patternDomain: ${patternDomain}) against current domain: ${currentDomain} - Match: ${isMatch}`);
    return isMatch;
  });
  
  console.log(`Should block ${url}: ${shouldBlock}`);
  return shouldBlock;
}

// Extract domain from URL
function extractDomain(url) {
  try {
    // Remove protocol and get domain
    let domain = url.replace(/^https?:\/\//, '');
    // Remove path if any
    domain = domain.split('/')[0];
    return domain.toLowerCase();
  } catch (e) {
    return url;
  }
}

// Helper function to normalize dates to YYYY-MM-DD for comparison
function toISODateString(date) {
  const d = new Date(date);
  const pad = num => (num < 10 ? '0' + num : num);
  return `${d.getUTCFullYear()}-${pad(d.getUTCMonth() + 1)}-${pad(d.getUTCDate())}`;
}

// Check if there's an active task scheduled for the current time
function hasActiveScheduledTask() {
  const now = new Date();
  const todayStr = toISODateString(now);
  
  console.log('\n=== Checking for active tasks ===');
  console.log('Current time:', now.toISOString(), 'Local:', now.toString());
  console.log('Today (YYYY-MM-DD):', todayStr);
  console.log('Total tasks to check:', tasks ? tasks.length : 0);
  
  if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
    console.log('No tasks to check or invalid tasks data');
    blockingStatus.currentTask = null;
    return false;
  }
  
  for (let i = 0; i < tasks.length; i++) {
    const task = tasks[i];
    try {
      console.log(`\n[${i+1}/${tasks.length}] Checking task:`, {
        id: task.id,
        title: task.title,
        scheduled: task.scheduled,
        archived: task.archived,
        scheduledStart: task.scheduledStart,
        scheduledEnd: task.scheduledEnd,
        isFullDay: task.isFullDay
      });
      
      // Skip if task is invalid, not scheduled, or archived
      if (!task || !task.scheduled || task.archived) {
        console.log('  → Skipping: Not scheduled, archived, or invalid task');
        continue;
      }
      
      // For full-day events
      if (task.isFullDay) {
        if (!task.scheduledStart) {
          console.log('  → Skipping: Full-day task is missing scheduledStart');
          continue;
        }
        
        const taskDate = new Date(task.scheduledStart);
        if (isNaN(taskDate.getTime())) {
          console.log('  → Skipping: Invalid start time for full-day task');
          continue;
        }
        
        const taskDateStr = toISODateString(taskDate);
        const isToday = taskDateStr === todayStr;
        
        console.log('  Full-day event details:', {
          taskDate: taskDate.toISOString(),
          taskDateLocal: taskDate.toString(),
          taskDateFormatted: taskDateStr,
          isToday,
          now: now.toISOString(),
          today: todayStr
        });
        
        if (isToday) {
          console.log(`✅ FOUND ACTIVE FULL-DAY TASK: "${task.title}" (${taskDateStr})`);
          blockingStatus.currentTask = task;
          return true;
        } else {
          console.log(`  Not active: Task is scheduled for ${taskDateStr} (${taskDate < now ? 'in the past' : 'in the future'})`);
        }
      } 
      // For time-based events
      else if (task.scheduledStart) {
        const startTime = new Date(task.scheduledStart);
        if (isNaN(startTime.getTime())) {
          console.log('  → Skipping: Invalid start time for time-based task');
          continue;
        }
        
        let endTime;
        if (task.scheduledEnd) {
          endTime = new Date(task.scheduledEnd);
          if (isNaN(endTime.getTime())) {
            console.log('  → Skipping: Invalid end time for time-based task');
            continue;
          }
        } else {
          // Default to 1 hour duration if no end time
          endTime = new Date(startTime);
          endTime.setHours(endTime.getHours() + 1);
        }
        
        const isActive = now >= startTime && now <= endTime;
        
        console.log('  Time-based event details:', {
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          now: now.toISOString(),
          isActive,
          isInFuture: now < startTime,
          isInPast: now > endTime
        });
        
        if (isActive) {
          console.log(`✅ FOUND ACTIVE TIME-BASED TASK: "${task.title}"`);
          console.log(`   Active from ${startTime.toISOString()} to ${endTime.toISOString()}`);
          blockingStatus.currentTask = task;
          return true;
        } else {
          console.log(`  Not active: ${now < startTime ? 'Not started yet' : 'Already ended'}`);
        }
      } else {
        console.log('  → Skipping: No valid schedule information');
      }
    } catch (error) {
      console.error('  Error checking task:', error);
      console.error('  Task data:', JSON.stringify(task, null, 2));
      continue;
    }
  }
  
  console.log('\n=== NO ACTIVE TASKS FOUND ===');
  blockingStatus.currentTask = null;
  return false;
}

// Load data from Chrome storage
function loadData() {
  return new Promise((resolve) => {
    chrome.storage.local.get(['blockedUrls', 'tasks', 'isBlockingEnabled', 'blockingStatus'], (result) => {
      if (result.blockedUrls) {
        blockedUrls = result.blockedUrls;
      }
      
      if (result.tasks) {
        tasks = result.tasks;
        // Log tasks for debugging
        console.log('Loaded tasks:', JSON.parse(JSON.stringify(tasks)));
      } else {
        console.log('No tasks found in storage');
        tasks = [];
      }
      
      if (result.isBlockingEnabled !== undefined) {
        isBlockingEnabled = result.isBlockingEnabled;
      }
      
      if (result.blockingStatus) {
        blockingStatus = result.blockingStatus;
      }
      
      console.log('Loaded data:', { 
        blockedUrls, 
        tasksCount: tasks.length, 
        isBlockingEnabled,
        blockingStatus,
        currentTime: new Date().toISOString()
      });
      
      // Check for active tasks immediately after loading
      const hasActive = hasActiveScheduledTask();
      console.log('Initial active task check:', hasActive ? 'Active task found' : 'No active tasks', {
        currentTask: blockingStatus.currentTask
      });
      
      resolve();
    });
  });
}

// Function to handle blocking a URL by redirecting to the block page
function handleUrlBlocking(url, taskTitle) {
  console.log('Handling URL blocking for:', url, 'Task:', taskTitle);
  
  // Increment blocked attempts counter
  blockingStatus.blockedAttempts++;
  
  // Update storage with the new counter
  chrome.storage.local.set({ blockingStatus });
  
  // Create the block page URL
  const blockPageUrl = chrome.runtime.getURL('block-page.html');
  console.log('Block page URL:', blockPageUrl);
  
  // Return the information needed for the content script to redirect
  return {
    shouldBlock: true,
    blockPageUrl: blockPageUrl,
    activeTask: { title: taskTitle },
    blockedAttempts: blockingStatus.blockedAttempts,
    timestamp: new Date().toISOString()
  };
}

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('=== New Message ===');
  console.log('Message received at:', new Date().toISOString());
  console.log('Message details:', JSON.parse(JSON.stringify(message)));
  
  // Handle check if URL should be blocked
  if (message.action === 'checkIfShouldBlock') {
    console.log('Processing checkIfShouldBlock for URL:', message.url);
    
    // Load data first, then check if URL should be blocked
    loadData().then(() => {
      console.log('Data loaded, checking for active tasks...');
      
      // Log current state before checking
      console.log('Current state:', {
        isBlockingEnabled,
        blockedUrls,
        tasksCount: tasks ? tasks.length : 0,
        currentTime: new Date().toISOString()
      });
      
      // Check if there's an active task
      const hasActiveTask = hasActiveScheduledTask();
      console.log('Has active task:', hasActiveTask);
      
      if (hasActiveTask) {
        console.log('Active task details:', JSON.parse(JSON.stringify(blockingStatus.currentTask)));
      } else {
        console.log('No active tasks found at:', new Date().toISOString());
      }
      
      // Check if URL should be blocked
      const shouldBlock = shouldBlockUrl(message.url);
      console.log('URL blocking check result for', message.url, ':', shouldBlock);
      
      // Only block if there's an active task and URL should be blocked
      if (hasActiveTask && shouldBlock) {
        console.log('Blocking URL during active task');
        // Get the active task title
        const activeTaskTitle = blockingStatus.currentTask ? blockingStatus.currentTask.title : 'Unknown Task';
        
        // Handle the blocking
        const blockResponse = handleUrlBlocking(message.url, activeTaskTitle);
        console.log('Sending block response:', blockResponse);
        
        // Send response with blocking information
        sendResponse(blockResponse);
      } else {
        const debugInfo = { 
          hasActiveTask, 
          shouldBlock,
          currentTask: blockingStatus.currentTask,
          currentTime: new Date().toISOString(),
          isBlockingEnabled,
          blockedUrls: blockedUrls,
          tasks: tasks ? tasks.map(t => ({
            id: t.id,
            title: t.title,
            scheduled: t.scheduled,
            scheduledStart: t.scheduledStart,
            scheduledEnd: t.scheduledEnd,
            isFullDay: t.isFullDay
          })) : []
        };
        
        console.log('Not blocking URL. Debug info:', debugInfo);
        
        // Send response that URL should not be blocked
        sendResponse({ 
          shouldBlock: false,
          debug: debugInfo
        });
      }
    }).catch(error => {
      console.error('Error in checkIfShouldBlock handler:', error);
      sendResponse({ 
        shouldBlock: false,
        error: error.message,
        stack: error.stack,
        currentTime: new Date().toISOString()
      });
    });
    
    // Return true to indicate we will send a response asynchronously
    return true;
  }
  
  // Handle opening CalenTask
  if (message.action === 'openCalenTask') {
    console.log('Opening CalenTask...');
    chrome.tabs.create({ url: 'todo.html' });
    return true;
  }
  
  console.warn('Unhandled message action:', message.action);
  return false;
});

// For debugging: Log when the background script loads
console.log('CalenTask background script loaded at', new Date().toLocaleString());

// Listen for changes to the storage
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'local') {
    if (changes.blockedUrls) {
      blockedUrls = changes.blockedUrls.newValue;
    }
    
    if (changes.tasks) {
      tasks = changes.tasks.newValue;
    }
    
    if (changes.isBlockingEnabled) {
      isBlockingEnabled = changes.isBlockingEnabled.newValue;
    }
  }
});

// Initialize data on startup
loadData();
